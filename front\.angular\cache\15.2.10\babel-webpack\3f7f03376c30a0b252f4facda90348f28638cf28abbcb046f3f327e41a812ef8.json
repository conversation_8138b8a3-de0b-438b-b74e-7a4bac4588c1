{"ast": null, "code": "import { TresorComposant } from '../../features/tresor.composant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/app.service\";\nimport * as i2 from \"src/app/store/services/back-end.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"ng-zorro-antd/icon\";\nimport * as i6 from \"ng-zorro-antd/date-picker\";\nimport * as i7 from \"ng-zorro-antd/select\";\nimport * as i8 from \"ng-zorro-antd/popover\";\nimport * as i9 from \"ng-zorro-antd/button\";\nimport * as i10 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i11 from \"ng-zorro-antd/core/wave\";\nimport * as i12 from \"../../../../../../../../shared/composants/form-item/form-item.component\";\nimport * as i13 from \"../../composants/form-banque-saisie/form-banque-saisie.component\";\nimport * as i14 from \"../../composants/banque-saisie/banque-saisie.component\";\nimport * as i15 from \"../../composants/banque-suivi/banque-suivi.component\";\nimport * as i16 from \"../../composants/banque-releve/banque-releve.component\";\nfunction PageBanqueComponent_nz_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-option\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function PageBanqueComponent_nz_option_6_Template_nz_option_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.getTotalSolde(ctx_r12.vars.journal));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const journal_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", journal_r11.numero)(\"nzLabel\", journal_r11.intitule);\n  }\n}\nfunction PageBanqueComponent_nz_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 30);\n  }\n  if (rf & 2) {\n    const year_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", year_r14)(\"nzLabel\", year_r14);\n  }\n}\nfunction PageBanqueComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"nz-date-picker\", 32);\n    i0.ɵɵlistener(\"ngModelChange\", function PageBanqueComponent_ng_template_24_Template_nz_date_picker_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.vars.date = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 33)(3, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PageBanqueComponent_ng_template_24_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.validationSalaire(ctx_r17.Actions.Add, {\n        journal: ctx_r17.vars.journal,\n        mois: ctx_r17.vars.mois.code,\n        annee: ctx_r17.vars.selected_year,\n        date: ctx_r17.vars.date,\n        isSuivis: false\n      }));\n    });\n    i0.ɵɵtext(4, \" Valider \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.vars.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ((tmp_1_0 = ctx_r3.getDataObject(\"bq_ecritures.total\")) == null ? null : tmp_1_0.validated) && !ctx_r3.vars.journal || ctx_r3.vars.plan != \"saisie\" || !ctx_r3.financialPrivilege());\n  }\n}\nfunction PageBanqueComponent_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"a\", 36);\n    i0.ɵɵlistener(\"click\", function PageBanqueComponent_ng_container_32_Template_a_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const mois_r18 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.selectMois(mois_r18));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const mois_r18 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzType\", (ctx_r4.vars.mois == null ? null : ctx_r4.vars.mois.code) == mois_r18.code ? \"primary\" : \"default\")(\"disabled\", !ctx_r4.vars.journal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", mois_r18.label, \" \");\n  }\n}\nfunction PageBanqueComponent_banque_saisie_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"banque-saisie\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"mois\", ctx_r5.vars.mois)(\"journal\", ctx_r5.vars.journal)(\"year\", ctx_r5.vars.selected_year);\n  }\n}\nfunction PageBanqueComponent_banque_suivi_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"banque-suivi\", 38);\n    i0.ɵɵlistener(\"isValidChange\", function PageBanqueComponent_banque_suivi_35_Template_banque_suivi_isValidChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.handleIsValidChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"mois\", ctx_r6.vars.mois)(\"journal\", ctx_r6.vars.journal)(\"year\", ctx_r6.vars.selected_year);\n  }\n}\nfunction PageBanqueComponent_banque_releve_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"banque-releve\", 39);\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"journal\", ctx_r7.vars.journal);\n  }\n}\nfunction PageBanqueComponent_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 1, ((tmp_0_0 = ctx_r8.getDataObject(\"bq_ecritures.total\")) == null ? null : tmp_0_0.total_solde) || 0, \".2-2\"), \" Dhs\");\n  }\n}\nfunction PageBanqueComponent_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 1, ((tmp_0_0 = ctx_r9.getDataObject(\"tresor.total_solde_bq\")) == null ? null : tmp_0_0.total_solde) || 0, \".2-2\"), \" Dhs\");\n  }\n}\nfunction PageBanqueComponent_form_banque_saisie_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"form-banque-saisie\");\n  }\n}\nexport class PageBanqueComponent extends TresorComposant {\n  constructor(app, backendService) {\n    super(app, backendService);\n    this.app = app;\n    this.backendService = backendService;\n    this.jours = [];\n    this.years = [];\n    this.cumul_journee = 0;\n  }\n  ngOnInit() {\n    this.setTitre('Gestion des Comptes Bancaire');\n    this.setPlan('saisie');\n    const currentYear = new Date().getFullYear();\n    const startYear = 2023;\n    this.vars.selected_year = currentYear;\n    for (let year = startYear; year <= currentYear; year++) {\n      this.years.push(year);\n    }\n    this.getCumulJournee();\n    this.getTotalSolde(this.vars.journal);\n  }\n  setPlan(pln) {\n    this.vars.plan = pln;\n    if (this.vars.mois && this.vars.plan == 'saisie') this.traiteEcritureBanque(this.Actions.Get, false, this.vars.journal, {\n      mois: this.vars.mois,\n      annee: this.vars.selected_year\n    });\n  }\n  handleIsValidChange(value) {\n    this.vars.isValid = value;\n    this.traiteEcritureBanque(this.Actions.Get, true, this.vars.journal, {\n      annee: this.vars.selected_year\n    }, value);\n  }\n  selectMois(mois) {\n    this.vars.mois = mois;\n    this.jours = this.listeJoursDuMois(this.vars.selected_year, +this.vars.mois.code, this.getConfig('dts'));\n    this.jours = this.jours.reduce((acc, currentArray) => acc.concat(currentArray), []);\n    if (this.vars.plan == 'saisie') {\n      this.traiteEcritureBanque(this.Actions.Get, false, this.vars.journal, {\n        mois: this.vars.mois,\n        annee: this.vars.selected_year\n      });\n    } else if (this.vars.plan == 'suivi') {\n      console.log(this.filtre.sh);\n      this.traiteEcritureBanque(this.Actions.Get, true, this.vars.journal, {\n        mois: this.vars.mois,\n        annee: this.vars.selected_year,\n        sh: this.getDataString('bq.intitule')\n      }, this.vars.isValid);\n    }\n  }\n}\nPageBanqueComponent.ɵfac = function PageBanqueComponent_Factory(t) {\n  return new (t || PageBanqueComponent)(i0.ɵɵdirectiveInject(i1.AppService), i0.ɵɵdirectiveInject(i2.BackEndService));\n};\nPageBanqueComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: PageBanqueComponent,\n  selectors: [[\"comp-page-banque\"]],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 42,\n  vars: 20,\n  consts: [[1, \"panel\", \"my-3\"], [1, \"mt-3\"], [1, \"grid\", \"grid-cols-3\", \"items-end\", \"gap-2\"], [1, \"flex\", \"space-x-4\"], [\"label\", \"Compte\", 1, \"w-[40%]\"], [1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [3, \"nzValue\", \"nzLabel\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Ann\\u00E9e\", 1, \"w-[40%]\"], [3, \"nzValue\", \"nzLabel\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"w-full\", \"justify-center\", \"gap-2\", \"uppercase\"], [\"nz-button\", \"\", 3, \"nzType\", \"disabled\", \"click\"], [1, \"flex\", \"w-full\", \"justify-end\", \"gap-2\", \"uppercase\"], [1, \"uppercase\", \"self-end\", \"text-end\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nz-popover\", \"\", \"nzPopoverPlacement\", \"bottom\", \"nzPopoverTrigger\", \"click\", 1, \"uppercase\", \"self-end\", 3, \"nzPopoverContent\", \"disabled\"], [1, \"flex\", \"items-center\", \"gap-2\"], [\"nz-icon\", \"\", \"nzType\", \"plus\", \"nzTheme\", \"outline\"], [\"formAddLub\", \"\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nz-button\", \"\", 1, \"uppercase\", 3, \"disabled\", \"click\"], [1, \"flex\", \"gap-2\"], [1, \"w-[5%]\"], [1, \"h-full\"], [4, \"ngFor\", \"ngForOf\"], [1, \"w-[95%]\"], [3, \"mois\", \"journal\", \"year\", 4, \"ngIf\"], [3, \"mois\", \"journal\", \"year\", \"isValidChange\", 4, \"ngIf\"], [3, \"journal\", 4, \"ngIf\"], [1, \"w-full\", \"flex\", \"gap-3\", \"items-end\", \"justify-end\", \"pt-4\"], [\"label\", \"Solde Bancaire\"], [4, \"ngIf\"], [3, \"nzValue\", \"nzLabel\", \"ngModelChange\"], [3, \"nzValue\", \"nzLabel\"], [1, \"w-[20vw]\", \"space-y-2\"], [\"nzAllowClear\", \"false\", 3, \"ngModel\", \"ngModelChange\"], [1, \"text-right\"], [\"nz-button\", \"\", 3, \"disabled\", \"click\"], [1, \"w-full\", \"p-2\"], [\"nz-button\", \"\", 1, \"w-full\", 3, \"nzType\", \"disabled\", \"click\"], [3, \"mois\", \"journal\", \"year\"], [3, \"mois\", \"journal\", \"year\", \"isValidChange\"], [3, \"journal\"]],\n  template: function PageBanqueComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"form-item\", 4)(5, \"nz-select\", 5);\n      i0.ɵɵlistener(\"ngModelChange\", function PageBanqueComponent_Template_nz_select_ngModelChange_5_listener($event) {\n        return ctx.vars.journal = $event;\n      })(\"ngModelChange\", function PageBanqueComponent_Template_nz_select_ngModelChange_5_listener() {\n        return ctx.getTotalSolde(ctx.vars.journal);\n      });\n      i0.ɵɵtemplate(6, PageBanqueComponent_nz_option_6_Template, 1, 2, \"nz-option\", 6);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(7, \"form-item\", 7)(8, \"nz-select\", 5);\n      i0.ɵɵlistener(\"ngModelChange\", function PageBanqueComponent_Template_nz_select_ngModelChange_8_listener($event) {\n        return ctx.vars.selected_year = $event;\n      })(\"ngModelChange\", function PageBanqueComponent_Template_nz_select_ngModelChange_8_listener() {\n        return ctx.selectMois(ctx.vars.mois);\n      });\n      i0.ɵɵtemplate(9, PageBanqueComponent_nz_option_9_Template, 1, 2, \"nz-option\", 8);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"div\", 9)(11, \"a\", 10);\n      i0.ɵɵlistener(\"click\", function PageBanqueComponent_Template_a_click_11_listener() {\n        return ctx.setPlan(\"saisie\");\n      });\n      i0.ɵɵtext(12, \" Saisie \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"a\", 10);\n      i0.ɵɵlistener(\"click\", function PageBanqueComponent_Template_a_click_13_listener() {\n        return ctx.setPlan(\"suivi\");\n      });\n      i0.ɵɵtext(14, \" Suivi / echeancier \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"a\", 10);\n      i0.ɵɵlistener(\"click\", function PageBanqueComponent_Template_a_click_15_listener() {\n        return ctx.setPlan(\"releve\");\n      });\n      i0.ɵɵtext(16, \" Relev\\u00E9 bancaire \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(17, \"div\", 11)(18, \"div\", 12)(19, \"a\", 13)(20, \"div\", 14);\n      i0.ɵɵelement(21, \"span\", 15);\n      i0.ɵɵelementStart(22, \"span\");\n      i0.ɵɵtext(23, \"Validation des salaires\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(24, PageBanqueComponent_ng_template_24_Template, 5, 2, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(26, \"a\", 17);\n      i0.ɵɵlistener(\"click\", function PageBanqueComponent_Template_a_click_26_listener() {\n        return ctx.openForm({\n          cle: \"form_banque\",\n          data: {\n            journal: ctx.vars.journal,\n            mois: ctx.vars.mois,\n            isSuivis: false\n          }\n        });\n      });\n      i0.ɵɵelementStart(27, \"span\");\n      i0.ɵɵtext(28, \"Ajouter\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(29, \"div\", 18)(30, \"div\", 19)(31, \"div\", 20);\n      i0.ɵɵtemplate(32, PageBanqueComponent_ng_container_32_Template, 4, 3, \"ng-container\", 21);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(33, \"div\", 22);\n      i0.ɵɵtemplate(34, PageBanqueComponent_banque_saisie_34_Template, 1, 3, \"banque-saisie\", 23);\n      i0.ɵɵtemplate(35, PageBanqueComponent_banque_suivi_35_Template, 1, 3, \"banque-suivi\", 24);\n      i0.ɵɵtemplate(36, PageBanqueComponent_banque_releve_36_Template, 1, 1, \"banque-releve\", 25);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(37, \"div\", 26)(38, \"form-item\", 27);\n      i0.ɵɵtemplate(39, PageBanqueComponent_span_39_Template, 3, 4, \"span\", 28);\n      i0.ɵɵtemplate(40, PageBanqueComponent_span_40_Template, 3, 4, \"span\", 28);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(41, PageBanqueComponent_form_banque_saisie_41_Template, 1, 0, \"form-banque-saisie\", 28);\n    }\n    if (rf & 2) {\n      const _r2 = i0.ɵɵreference(25);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.vars.journal);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.listeJournaux(\"bq\", ctx.getEntrepot()));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngModel\", ctx.vars.selected_year);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.years);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"nzType\", ctx.vars.plan == \"saisie\" ? \"primary\" : \"default\")(\"disabled\", !ctx.vars.journal);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"nzType\", ctx.vars.plan == \"suivi\" ? \"primary\" : \"default\")(\"disabled\", !ctx.vars.journal);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"nzType\", ctx.vars.plan == \"releve\" ? \"primary\" : \"default\")(\"disabled\", !ctx.vars.journal);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"nzPopoverContent\", _r2)(\"disabled\", !ctx.achatCommercialPrivilege());\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"disabled\", !ctx.vars.journal || ctx.vars.plan != \"saisie\" || !ctx.vars.mois || !ctx.financialPrivilege());\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngForOf\", ctx.getDataList(\"commun.liste_mois\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.vars.journal && ctx.vars.plan == \"saisie\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.vars.journal && ctx.vars.plan == \"suivi\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.vars.journal && ctx.vars.plan == \"releve\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.vars.plan == \"releve\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.vars.plan != \"releve\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isFormVisible(\"form_banque\"));\n    }\n  },\n  dependencies: [i3.NgForOf, i3.NgIf, i4.NgControlStatus, i4.NgModel, i5.NzIconDirective, i6.NzDatePickerComponent, i7.NzOptionComponent, i7.NzSelectComponent, i8.NzPopoverDirective, i9.NzButtonComponent, i10.ɵNzTransitionPatchDirective, i11.NzWaveDirective, i12.FormItemComponent, i13.FormBanqueSaisieComponent, i14.BanqueSaisieComponent, i15.BanqueSuiviComponent, i16.BanqueReleveComponent, i3.DecimalPipe],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "mappings": "AAGA,SAASA,eAAe,QAAQ,iCAAiC;;;;;;;;;;;;;;;;;;;;;ICGzCC,qCAC+E;IAA9CA;MAAAA;MAAA;MAAA,OAAiBA,0DAA2B;IAAA,EAAC;IAACA,iBAAY;;;;IADrBA,4CAA0B;;;;;IAMhGA,gCAAoF;;;;IAA9CA,kCAAgB;;;;;;IA0B1DA,+BAAgC;IACSA;MAAAA;MAAA;MAAA,OAAaA,0CAAiB;IAAA,EAAP;IAACA,iBAAiB;IAC9EA,+BAAwB;IACFA;MAAAA;MAAA;MAAA,OAAWA;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC,UAKV;MAC/C,EAAmC;IAAA;IACHL,yBACJ;IAAAA,iBAAS;;;;;IAVwBA,eAAuB;IAAvBA,0CAAuB;IAQhDA,eAA6H;IAA7HA,8MAA6H;;;;;;IAyB7IA,6BAAoE;IAChEA,+BAAwB;IAEWA;MAAA;MAAA;MAAA;MAAA,OAASA,2CAAgB;IAAA,EAAC;IACrDA,YACJ;IAAAA,iBAAI;IAEZA,0BAAe;;;;;IALqBA,eAA+D;IAA/DA,2HAA+D;IAEvFA,eACJ;IADIA,+CACJ;;;;;IAMZA,oCACkC;;;;IAD2BA,uCAAkB;;;;;;IAE/EA,wCACgF;IAAhDA;MAAAA;MAAA;MAAA,OAAiBA,kDAA2B;IAAA,EAAC;IAD7EA,iBACgF;;;;IADrBA,uCAAkB;;;;;IAE7EA,oCAAwF;;;;IAAzEA,6CAAwB;;;;;IAKvCA,4BAAoC;IAAAA,YACV;;IAAAA,iBAAO;;;;;IADGA,eACV;IADUA,mKACV;;;;;IAC1BA,4BAAoC;IAAAA,YACjB;;IAAAA,iBAAO;;;;;IADUA,eACjB;IADiBA,sKACjB;;;;;IAKnCA,qCAA8E;;;AD1F9E,OAAM,MAAOM,mBAAoB,SAAQP,eAAe;EACtDQ,YACqBC,GAAe,EACfC,cAA8B;IAEjD,KAAK,CAACD,GAAG,EAAEC,cAAc,CAAC;IAHP,QAAG,GAAHD,GAAG;IACH,mBAAc,GAAdC,cAAc;IAInC,UAAK,GAAU,EAAE;IACjB,UAAK,GAAa,EAAE;IACpB,kBAAa,GAAG,CAAC;EAHjB;EAIAC,QAAQ;IACN,IAAI,CAACC,QAAQ,CAAC,8BAA8B,CAAC;IAC7C,IAAI,CAACC,OAAO,CAAC,QAAQ,CAAC;IACtB,MAAMC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAC5C,MAAMC,SAAS,GAAG,IAAI;IACtB,IAAI,CAACC,IAAI,CAACC,aAAa,GAAGL,WAAW;IACrC,KAAK,IAAIM,IAAI,GAAGH,SAAS,EAAEG,IAAI,IAAIN,WAAW,EAAEM,IAAI,EAAE,EAAE;MACtD,IAAI,CAACC,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC;;IAEvB,IAAI,CAACG,eAAe,EAAE;IACtB,IAAI,CAACC,aAAa,CAAC,IAAI,CAACN,IAAI,CAAChB,OAAO,CAAC;EACvC;EAEAW,OAAO,CAACY,GAAW;IACjB,IAAI,CAACP,IAAI,CAACQ,IAAI,GAAGD,GAAG;IACpB,IAAI,IAAI,CAACP,IAAI,CAACf,IAAI,IAAI,IAAI,CAACe,IAAI,CAACQ,IAAI,IAAI,QAAQ,EAC9C,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACC,OAAO,CAACC,GAAG,EAAE,KAAK,EAAE,IAAI,CAACX,IAAI,CAAChB,OAAO,EAAE;MAAEC,IAAI,EAAE,IAAI,CAACe,IAAI,CAACf,IAAI;MAAEC,KAAK,EAAE,IAAI,CAACc,IAAI,CAACC;IAAa,CAAE,CAAC;EACnI;EACAW,mBAAmB,CAACC,KAAc;IAChC,IAAI,CAACb,IAAI,CAACc,OAAO,GAAGD,KAAK;IACzB,IAAI,CAACJ,oBAAoB,CAAC,IAAI,CAACC,OAAO,CAACC,GAAG,EAAE,IAAI,EAAE,IAAI,CAACX,IAAI,CAAChB,OAAO,EAAE;MAACE,KAAK,EAAE,IAAI,CAACc,IAAI,CAACC;IAAa,CAAE,EAAEY,KAAK,CAAC;EAChH;EACAE,UAAU,CAAC9B,IAAS;IAClB,IAAI,CAACe,IAAI,CAACf,IAAI,GAAGA,IAAI;IACrB,IAAI,CAAC+B,KAAK,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACjB,IAAI,CAACC,aAAa,EAAE,CAAC,IAAI,CAACD,IAAI,CAACf,IAAI,CAACiC,IAAI,EAAE,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC;IACxG,IAAI,CAACH,KAAK,GAAG,IAAI,CAACA,KAAK,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,YAAY,KAAKD,GAAG,CAACE,MAAM,CAACD,YAAY,CAAC,EAAE,EAAE,CAAC;IACnF,IAAI,IAAI,CAACtB,IAAI,CAACQ,IAAI,IAAI,QAAQ,EAAE;MAC9B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACC,OAAO,CAACC,GAAG,EAAE,KAAK,EAAE,IAAI,CAACX,IAAI,CAAChB,OAAO,EAAE;QAAEC,IAAI,EAAE,IAAI,CAACe,IAAI,CAACf,IAAI;QAAEC,KAAK,EAAE,IAAI,CAACc,IAAI,CAACC;MAAa,CAAE,CAAC;KAChI,MACI,IAAI,IAAI,CAACD,IAAI,CAACQ,IAAI,IAAI,OAAO,EAAC;MACjCgB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,CAACC,EAAE,CAAC;MAC3B,IAAI,CAAClB,oBAAoB,CAAC,IAAI,CAACC,OAAO,CAACC,GAAG,EAAE,IAAI,EAAE,IAAI,CAACX,IAAI,CAAChB,OAAO,EAAE;QAAEC,IAAI,EAAE,IAAI,CAACe,IAAI,CAACf,IAAI;QAAEC,KAAK,EAAE,IAAI,CAACc,IAAI,CAACC,aAAa;QAAE0B,EAAE,EAAE,IAAI,CAACC,aAAa,CAAC,aAAa;MAAC,CAAC,EAAE,IAAI,CAAC5B,IAAI,CAACc,OAAO,CAAC;;EAE3L;;AA3CWzB,mBAAoB;mBAApBA,mBAAmB;AAAA;AAAnBA,mBAAoB;QAApBA,mBAAmB;EAAAwC;EAAAC;EAAAC;EAAA/B;EAAAgC;EAAAC;IAAA;MCVhClD,8BAAwB;MAKsBA;QAAA;MAAA,EAA0B;QAAA,OAAkBmD,mCAA2B;MAAA,EAA7C;MAChDnD,gFAC2F;MAC/FA,iBAAY;MAEhBA,oCAAyC;MACXA;QAAA;MAAA,EAAgC;QAAA,OAAkBmD,6BAAqB;MAAA,EAAvC;MACtDnD,gFAAoF;MACxFA,iBAAY;MAGpBA,+BAAwD;MAEhDA;QAAA,OAASmD,YAAQ,QAAQ,CAAC;MAAA,EAAC;MAAEnD,yBAAO;MAAAA,iBAAI;MAC5CA,8BAC+B;MAA3BA;QAAA,OAASmD,YAAQ,OAAO,CAAC;MAAA,EAAC;MAC1BnD,qCACJ;MAAAA,iBAAI;MAAAA,8BAC4B;MAA5BA;QAAA,OAASmD,YAAQ,QAAQ,CAAC;MAAA,EAAC;MAC3BnD,uCACJ;MAAAA,iBAAI;MAERA,gCAAqD;MAKrCA,4BAAqD;MACrDA,6BAAM;MAAAA,wCAAuB;MAAAA,iBAAO;MAIhDA,wHAec;MACdA,8BAWwB;MATpBA;QAAA,OAENmD;UAAAC,KACJ,aAAa;UAAAC;YAAApD;YAAAC;YAAAG,UAIJ;UAAK;QAAA,EAEA;MAAA,EAAH;MACDL,6BAAM;MAAAA,wBAAO;MAAAA,iBAAO;MAIhCA,gCAAwB;MAGZA,yFAOe;MACnBA,iBAAM;MAEVA,gCAAqB;MACjBA,2FACkC;MAClCA,yFACgF;MAChFA,2FAAwF;MAC5FA,iBAAM;MAEVA,gCAA0D;MAElDA,yEACiC;MACjCA,yEAC0B;MAC9BA,iBAAY;MAIxBA,qGAA8E;;;;MA/FhCA,eAA0B;MAA1BA,0CAA0B;MACjBA,eAAqC;MAArCA,oEAAqC;MAK9CA,eAAgC;MAAhCA,gDAAgC;MAC1BA,eAAQ;MAARA,mCAAQ;MAK/BA,eAAwD;MAAxDA,0EAAwD;MAExDA,eAAuD;MAAvDA,yEAAuD;MAGnDA,eAAwD;MAAxDA,0EAAwD;MAOAA,eAA+B;MAA/BA,sCAA+B;MAyBpGA,eAA0F;MAA1FA,wHAA0F;MAkB3DA,eAAmC;MAAnCA,8DAAmC;MAWtDA,eAA2C;MAA3CA,oEAA2C;MAE5CA,eAA0C;MAA1CA,mEAA0C;MAEhBA,eAA2C;MAA3CA,oEAA2C;MAK7EA,eAA2B;MAA3BA,gDAA2B;MAE3BA,eAA2B;MAA3BA,gDAA2B;MAM7BA,eAAkC;MAAlCA,uDAAkC", "names": ["TresorComposant", "i0", "journal", "mois", "annee", "date", "<PERSON><PERSON><PERSON><PERSON>", "PageBanqueComponent", "constructor", "app", "backendService", "ngOnInit", "setTitre", "setPlan", "currentYear", "Date", "getFullYear", "startYear", "vars", "selected_year", "year", "years", "push", "getCumulJournee", "getTotalSolde", "pln", "plan", "traiteEcritureBanque", "Actions", "Get", "handleIsValidChange", "value", "<PERSON><PERSON><PERSON><PERSON>", "selectMois", "jours", "listeJoursDuMois", "code", "getConfig", "reduce", "acc", "currentArray", "concat", "console", "log", "filtre", "sh", "getDataString", "selectors", "features", "decls", "consts", "template", "ctx", "cle", "data"], "sourceRoot": "", "sources": ["G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\compta\\modules\\tresor\\vues\\page-banque\\page-banque.component.ts", "G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\compta\\modules\\tresor\\vues\\page-banque\\page-banque.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AppService } from 'src/app/core/services/app.service';\nimport { BackEndService } from 'src/app/store/services/back-end.service';\nimport { TresorComposant } from '../../features/tresor.composant';\n\n@Component({\n  selector: 'comp-page-banque',\n  templateUrl: './page-banque.component.html',\n  styleUrls: ['./page-banque.component.scss']\n})\nexport class PageBanqueComponent extends TresorComposant implements OnInit {\n  constructor(\n    protected override app: AppService,\n    protected override backendService: BackEndService\n  ) {\n    super(app, backendService);\n  }\n  jours: any[] = [];\n  years: number[] = [];\n  cumul_journee = 0;\n  ngOnInit(): void {\n    this.setTitre('Gestion des Comptes Bancaire');\n    this.setPlan('saisie')\n    const currentYear = new Date().getFullYear();\n    const startYear = 2023;\n    this.vars.selected_year = currentYear\n    for (let year = startYear; year <= currentYear; year++) {\n      this.years.push(year);\n    }\n    this.getCumulJournee()\n    this.getTotalSolde(this.vars.journal)\n  }\n\n  setPlan(pln: string) {\n    this.vars.plan = pln\n    if (this.vars.mois && this.vars.plan == 'saisie')\n      this.traiteEcritureBanque(this.Actions.Get, false, this.vars.journal, { mois: this.vars.mois, annee: this.vars.selected_year })\n  }\n  handleIsValidChange(value: boolean) {\n    this.vars.isValid = value;\n    this.traiteEcritureBanque(this.Actions.Get, true, this.vars.journal, {annee: this.vars.selected_year }, value)\n  }\n  selectMois(mois: any) {\n    this.vars.mois = mois;\n    this.jours = this.listeJoursDuMois(this.vars.selected_year, +this.vars.mois.code, this.getConfig('dts'))\n    this.jours = this.jours.reduce((acc, currentArray) => acc.concat(currentArray), []);\n    if (this.vars.plan == 'saisie') {\n      this.traiteEcritureBanque(this.Actions.Get, false, this.vars.journal, { mois: this.vars.mois, annee: this.vars.selected_year })\n    }\n    else if (this.vars.plan == 'suivi'){\n      console.log(this.filtre.sh);\n      this.traiteEcritureBanque(this.Actions.Get, true, this.vars.journal, { mois: this.vars.mois, annee: this.vars.selected_year, sh: this.getDataString('bq.intitule')}, this.vars.isValid)\n    }\n  }\n}", "<div class=\"panel my-3\">\n    <div class=\"mt-3\">\n        <div class=\"grid grid-cols-3 items-end gap-2\">\n            <div class=\"flex space-x-4\">\n                <form-item label=\"Compte\" class=\"w-[40%]\">\n                    <nz-select class=\"w-full\" [(ngModel)]=\"vars.journal\" (ngModelChange)=\"getTotalSolde(vars.journal)\">\n                        <nz-option *ngFor=\"let journal of listeJournaux('bq', getEntrepot())\" [nzValue]=\"journal.numero\"\n                            [nzLabel]=\"journal.intitule\" (ngModelChange)=\"getTotalSolde(vars.journal)\"></nz-option>\n                    </nz-select>\n                </form-item>\n                <form-item label=\"Année\" class=\"w-[40%]\">\n                    <nz-select class=\"w-full\" [(ngModel)]=\"vars.selected_year\" (ngModelChange)=\"selectMois(vars.mois)\">\n                        <nz-option *ngFor=\"let year of years\" [nzValue]=\"year\" [nzLabel]=\"year\"></nz-option>\n                    </nz-select>\n                </form-item>\n            </div>\n            <div class=\"flex w-full justify-center gap-2 uppercase\">\n                <a nz-button [nzType]=\"vars.plan == 'saisie' ? 'primary' : 'default'\" [disabled]=\"!vars.journal\"\n                    (click)=\"setPlan('saisie')\"> Saisie </a>\n                <a nz-button [nzType]=\"vars.plan == 'suivi' ? 'primary' : 'default'\" [disabled]=\"!vars.journal\"\n                    (click)=\"setPlan('suivi')\">\n                    Suivi / echeancier\n                </a><a nz-button [nzType]=\"vars.plan == 'releve' ? 'primary' : 'default'\" [disabled]=\"!vars.journal\"\n                    (click)=\"setPlan('releve')\">\n                    Relevé bancaire\n                </a>\n            </div>\n            <div class=\"flex w-full justify-end gap-2 uppercase\">\n                <div class=\"uppercase self-end text-end\">\n                    <a nz-button nzType=\"primary\" nz-popover nzPopoverPlacement=\"bottom\" [nzPopoverContent]=\"formAddLub\"\n                        nzPopoverTrigger=\"click\" [disabled]=\"!achatCommercialPrivilege()\" class=\"uppercase self-end\">\n                        <div class=\"flex items-center gap-2\">\n                            <span nz-icon nzType=\"plus\" nzTheme=\"outline\"></span>\n                            <span>Validation des salaires</span>\n                        </div>\n                    </a>\n                </div>\n                <ng-template #formAddLub>\n                    <div class=\"w-[20vw] space-y-2\">\n                        <nz-date-picker nzAllowClear=\"false\" [(ngModel)]=\"vars.date\"></nz-date-picker>\n                        <div class=\"text-right\">\n                            <button nz-button (click)=\" validationSalaire(Actions.Add, {\n                                    journal: vars.journal,\n                                    mois: vars.mois.code,\n                                    annee: vars.selected_year,\n                                    date : vars.date,\n                                    isSuivis: false\n                                })\" [disabled]=\"getDataObject('bq_ecritures.total')?.validated && !vars.journal || vars.plan != 'saisie'|| !financialPrivilege()\">\n                                Valider\n                            </button>\n                        </div>\n                    </div>\n                </ng-template>\n                <a nz-button nzType=\"primary\"\n                    [disabled]=\"!vars.journal || vars.plan != 'saisie' || !vars.mois || !financialPrivilege()\" nz-button\n                    (click)=\"\n                        openForm({\n                            cle: 'form_banque',\n                            data: {\n                                journal: vars.journal,\n                                mois: vars.mois,\n                                isSuivis:false\n                            }\n                        })\n                    \" class=\"uppercase\">\n                    <span>Ajouter</span>\n                </a>\n            </div>\n        </div>\n        <div class=\"flex gap-2\">\n            <div class=\"w-[5%]\">\n                <div class=\"h-full\">\n                    <ng-container *ngFor=\"let mois of getDataList('commun.liste_mois')\">\n                        <div class=\"w-full p-2\">\n                            <a class=\"w-full\" nz-button [nzType]=\"vars.mois?.code == mois.code ? 'primary' : 'default'\"\n                                [disabled]=\"!vars.journal\" (click)=\"selectMois(mois)\">\n                                {{ mois.label }}\n                            </a>\n                        </div>\n                    </ng-container>\n                </div>\n            </div>\n            <div class=\"w-[95%]\">\n                <banque-saisie *ngIf=\"vars.journal && vars.plan == 'saisie'\" [mois]=\"vars.mois\" [journal]=\"vars.journal\"\n                    [year]=\"vars.selected_year\" />\n                <banque-suivi *ngIf=\"vars.journal && vars.plan == 'suivi'\" [mois]=\"vars.mois\" [journal]=\"vars.journal\"\n                    [year]=\"vars.selected_year\" (isValidChange)=\"handleIsValidChange($event)\" />\n                <banque-releve [journal]=\"vars.journal\" *ngIf=\"vars.journal && vars.plan == 'releve'\" />\n            </div>\n        </div>\n        <div class=\"w-full flex gap-3 items-end justify-end pt-4\">\n            <form-item label=\"Solde Bancaire\">\n                <span *ngIf=\"vars.plan == 'releve'\">{{ getDataObject('bq_ecritures.total')?.total_solde || 0 |\n                    number : \".2-2\" }} Dhs</span>\n                <span *ngIf=\"vars.plan != 'releve'\">{{ getDataObject('tresor.total_solde_bq')?.total_solde || 0 | number\n                    : \".2-2\" }} Dhs</span>\n            </form-item>\n        </div>\n    </div>\n</div>\n<form-banque-saisie *ngIf=\"isFormVisible('form_banque')\"></form-banque-saisie>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}