{"ast": null, "code": "import { filter } from 'rxjs';\nimport { Composant } from 'src/app/core/types/composant.base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/app.service\";\nimport * as i2 from \"@angular/common\";\nfunction ReceptionPrintComponent_div_2_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Prix TTC \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \"TVA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Total TTC \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_60_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lg_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 1, (lg_r7.data_pricing == null ? null : lg_r7.data_pricing.prix_ttc) || 0, \".2-2\", \"fr\"), \" \");\n  }\n}\nconst _c0 = function (a2) {\n  return [\"val\", \"==\", a2];\n};\nfunction ReceptionPrintComponent_div_2_div_60_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lg_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getDataList(\"commun.liste_tva\", i0.ɵɵpureFunction1(1, _c0, lg_r7.data_pricing == null ? null : lg_r7.data_pricing.tva))[0] == null ? null : ctx_r9.getDataList(\"commun.liste_tva\", i0.ɵɵpureFunction1(3, _c0, lg_r7.data_pricing == null ? null : lg_r7.data_pricing.tva))[0].label, \" \");\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_60_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lg_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 1, (lg_r7.data_pricing == null ? null : lg_r7.data_pricing.prix_ttc) * (lg_r7.data_qte == null ? null : lg_r7.data_qte.abr) || 0, \".2-2\", \"fr\"), \" \");\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_60_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const total_r16 = ctx.ngIf;\n    const ctx_r15 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Total HT : \", ctx_r15.doc.is_avoir ? -total_r16.mt_ht : i0.ɵɵpipeBind3(3, 2, total_r16.mt_ht, \".2-2\", \"fr\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Total TTC : \", ctx_r15.doc.is_avoir ? -total_r16.mt_ttc : i0.ɵɵpipeBind3(6, 6, total_r16.mt_ttc, \".2-2\", \"fr\"), \"\");\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_60_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, ReceptionPrintComponent_div_2_div_60_div_11_div_1_Template, 7, 10, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.doc.data_valeurs);\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"div\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ReceptionPrintComponent_div_2_div_60_div_8_Template, 3, 5, \"div\", 24);\n    i0.ɵɵtemplate(9, ReceptionPrintComponent_div_2_div_60_div_9_Template, 2, 5, \"div\", 24);\n    i0.ɵɵtemplate(10, ReceptionPrintComponent_div_2_div_60_div_10_Template, 3, 5, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ReceptionPrintComponent_div_2_div_60_div_11_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lg_r7 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", lg_r7.data_produit == null ? null : lg_r7.data_produit.code, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", lg_r7.data_produit == null ? null : lg_r7.data_produit.libelle, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", lg_r7.data_qte == null ? null : lg_r7.data_qte.abr, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.withPrice);\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_61_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 1, (ctx_r18.lg.data_pricing == null ? null : ctx_r18.lg.data_pricing.prix_ttc) || 0, \".2-2\", \"fr\"), \" \");\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_61_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getDataList(\"commun.liste_tva\", i0.ɵɵpureFunction1(1, _c0, ctx_r19.lg.data_pricing == null ? null : ctx_r19.lg.data_pricing.tva))[0] == null ? null : ctx_r19.getDataList(\"commun.liste_tva\", i0.ɵɵpureFunction1(3, _c0, ctx_r19.lg.data_pricing == null ? null : ctx_r19.lg.data_pricing.tva))[0].label, \" \");\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_61_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 1, (ctx_r20.lg.data_pricing == null ? null : ctx_r20.lg.data_pricing.prix_ttc) * (ctx_r20.lg.data_qte == null ? null : ctx_r20.lg.data_qte.abr) || 0, \".2-2\", \"fr\"), \" \");\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_61_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const total_r23 = ctx.ngIf;\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Total HT : \", ctx_r22.doc.is_avoir ? -total_r23.mt_ht : i0.ɵɵpipeBind3(3, 2, total_r23.mt_ht, \".2-2\", \"fr\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Total TTC : \", ctx_r22.doc.is_avoir ? -total_r23.mt_ttc : i0.ɵɵpipeBind3(6, 6, total_r23.mt_ttc, \".2-2\", \"fr\"), \"\");\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_61_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, ReceptionPrintComponent_div_2_div_61_div_11_div_1_Template, 7, 10, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.doc.data_valeurs);\n  }\n}\nfunction ReceptionPrintComponent_div_2_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"div\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ReceptionPrintComponent_div_2_div_61_div_8_Template, 3, 5, \"div\", 24);\n    i0.ɵɵtemplate(9, ReceptionPrintComponent_div_2_div_61_div_9_Template, 2, 5, \"div\", 24);\n    i0.ɵɵtemplate(10, ReceptionPrintComponent_div_2_div_61_div_10_Template, 3, 5, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ReceptionPrintComponent_div_2_div_61_div_11_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.lg.data_produit == null ? null : ctx_r6.lg.data_produit.code, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.lg.data_produit == null ? null : ctx_r6.lg.data_produit.libelle, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.lg.data_qte == null ? null : ctx_r6.lg.data_qte.abr, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.withPrice);\n  }\n}\nfunction ReceptionPrintComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\");\n    i0.ɵɵelement(5, \"img\", 7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 8);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 9);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 10)(12, \"div\", 11)(13, \"div\", 12);\n    i0.ɵɵtext(14, \"Fournisseur : \");\n    i0.ɵɵelementStart(15, \"span\", 13);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"div\", 15)(19, \"div\", 16)(20, \"div\", 17);\n    i0.ɵɵtext(21, \"ice : \");\n    i0.ɵɵelementStart(22, \"span\", 13);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 17);\n    i0.ɵɵtext(25, \"Adresse : \");\n    i0.ɵɵelementStart(26, \"span\", 13);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 17);\n    i0.ɵɵtext(29, \"t\\u00E9l\\u00E9phone : \");\n    i0.ɵɵelementStart(30, \"span\", 13);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"div\", 11)(33, \"div\", 12);\n    i0.ɵɵtext(34, \"Camion : \");\n    i0.ɵɵelementStart(35, \"span\", 13);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 14)(38, \"div\", 15)(39, \"div\", 16)(40, \"div\", 17);\n    i0.ɵɵtext(41, \"Chauffeur : \");\n    i0.ɵɵelementStart(42, \"span\", 13);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 17);\n    i0.ɵɵtext(45, \"t\\u00E9l\\u00E9phone : \");\n    i0.ɵɵelementStart(46, \"span\", 13);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(48, \"div\", 18)(49, \"div\", 19)(50, \"div\", 20)(51, \"div\", 21);\n    i0.ɵɵtext(52, \" R\\u00E9f\\u00E9rence \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 22);\n    i0.ɵɵtext(54, \" D\\u00E9signation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 23);\n    i0.ɵɵtext(56, \" Quantit\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(57, ReceptionPrintComponent_div_2_div_57_Template, 2, 0, \"div\", 24);\n    i0.ɵɵtemplate(58, ReceptionPrintComponent_div_2_div_58_Template, 2, 0, \"div\", 24);\n    i0.ɵɵtemplate(59, ReceptionPrintComponent_div_2_div_59_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(60, ReceptionPrintComponent_div_2_div_60_Template, 12, 7, \"div\", 25);\n    i0.ɵɵtemplate(61, ReceptionPrintComponent_div_2_div_61_Template, 12, 7, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\", 27);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 28);\n    i0.ɵɵtext(65, \" TOP PETROL - BUSINESS CENTRE YASMINA 1ER ETG AIN SEBAA CASABLANCA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 29);\n    i0.ɵɵtext(67, \"IF:1687898 - ICE: 000164488000066 - Patente: 31990429 - FIX: 0522343920\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const num_page_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 17, ctx_r0.doc.date_doc, \"dd-MM-yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.doc.is_avoir ? \"Avoir fournisseur \" : \"Bon de R\\u00E9ception N\\u00B0 \", \" \", ctx_r0.doc.numero_interne, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.doc.data_tiers == null ? null : ctx_r0.doc.data_tiers.nom);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.cls == null ? null : ctx_r0.cls.data_statut == null ? null : ctx_r0.cls.data_statut.ice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.cls == null ? null : ctx_r0.cls.data_adresses[\"f\"] == null ? null : ctx_r0.cls.data_adresses[\"f\"].adre);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.cls == null ? null : ctx_r0.cls.data_adresses[\"f\"] == null ? null : ctx_r0.cls.data_adresses[\"f\"].tel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.doc.data_camion == null ? null : ctx_r0.doc.data_camion.mat);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.doc.data_camion == null ? null : ctx_r0.doc.data_camion.ch);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.doc.data_camion == null ? null : ctx_r0.doc.data_camion.tel);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.withPrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.pages[num_page_r1].lignes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.doc.data_frais.lst);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Page : \", num_page_r1, \" / \", ctx_r0.nbPages, \"\");\n  }\n}\nexport class ReceptionPrintComponent extends Composant {\n  constructor(app) {\n    super(app);\n    this.app = app;\n    this.code = 'page_rc';\n    this.doc = {};\n    this.pages = {};\n    this.nbPages = 1;\n    this.details = [];\n    this.cls = {};\n    this.withPrice = false;\n  }\n  ngOnInit() {\n    this.app.getPrintStream()\n    // .asObservable()\n    .pipe(filter(printData => {\n      return printData?.code == this.code;\n    })).subscribe(printData => {\n      if (!printData.consumed) {\n        this.withPrice = printData?.checked;\n        this.doc = printData.data?.doc;\n        this.pages = this.gerenatePages(printData.data?.details);\n        this.details = printData.data?.details;\n        this.nbPages = Object.keys(this.pages).length;\n        if (this.doc.data_tiers) {\n          this.cls = this.getDataList(`tiers.liste_frs`).find(ob => ob.code_tiers == this.doc.data_tiers.code);\n        }\n        setTimeout(() => {\n          if (printData.consumed) return;\n          this.appercu(this.code);\n          printData.consumed = true;\n        }, 300);\n      }\n    });\n  }\n  gerenatePages(det) {\n    let result = [];\n    const listeFamilles = [...new Set(det.map(det => {\n      const fam = det.data_produit.famille?.split('.');\n      return fam[1] || '';\n    }))];\n    listeFamilles.forEach(fam => {\n      const lgFam = {\n        fam,\n        tpe: 'fam'\n      };\n      const famille = this.getDataList('product.liste_familles').find(f => f.code == fam);\n      if (famille) {\n        lgFam.libelle = famille.libelle;\n      }\n      // result.push(lgFam);\n      result = result.concat(det.filter(lg => lg.data_produit.famille.endsWith(`.${fam}`)));\n    });\n    //18\n    const nbLigneParPage = 13;\n    const pages = {};\n    // const nbPage = Math.ceil(result.length / nbLigneParPage);\n    let pageEnCours = 1;\n    let nbLigne = 0;\n    result.forEach((lg, ndx) => {\n      if (nbLigne > nbLigneParPage) {\n        pageEnCours += 1;\n        nbLigne = 0;\n      }\n      if (!pages[pageEnCours]) {\n        pages[pageEnCours] = {\n          lignes: []\n        };\n      }\n      pages[pageEnCours].lignes.push(lg);\n      nbLigne += 1;\n    });\n    return pages;\n  }\n  calculateTotal() {\n    let ttc = 0;\n    let ht = 0;\n    let tva = {};\n    this.getDataList(\"commun.liste_tva\").forEach(tv => {\n      tva[tv.val] = 0;\n    });\n    for (let lg of this.details) {\n      ht += lg.data_pricing.prix_ht * lg.data_qte.abr;\n      ttc += lg.data_pricing.prix_ht * lg.data_qte.abr * lg.data_pricing.tva;\n      tva[lg.data_pricing.tva] += lg.data_pricing.prix_ht * lg.data_qte.abr * lg.data_pricing.tva - lg.data_pricing.prix_ht * lg.data_qte.vcc;\n    }\n    this.getDataList(\"commun.liste_tva\").forEach(tv => {\n      if (tva[tv.val] == 0) {\n        delete tva[tv.val];\n      }\n    });\n    return {\n      ttc,\n      ht,\n      tva\n    };\n  }\n  get_num_recep(doc) {\n    if (doc && doc.is_avoir && doc.numero_interne) {\n      return doc.numero_interne.split('F')[1];\n    }\n    return doc.numero_interne;\n  }\n}\nReceptionPrintComponent.ɵfac = function ReceptionPrintComponent_Factory(t) {\n  return new (t || ReceptionPrintComponent)(i0.ɵɵdirectiveInject(i1.AppService));\n};\nReceptionPrintComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ReceptionPrintComponent,\n  selectors: [[\"reception-print\"]],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 3,\n  vars: 2,\n  consts: [[1, \"hidden\"], [3, \"id\"], [\"class\", \"next-page relative h-[100vh] p-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"next-page\", \"relative\", \"h-[100vh]\", \"p-2\"], [1, \"overflow-hidden\", \"flex\", \"items-end\", \"p-2\", \"border-0\"], [1, \"basis-[90%]\"], [1, \"w-[50%]\", \"flex\", \"justify-center\"], [\"src\", \"./assets/logo-top-petrol.png\"], [1, \"pr-2\", \"flex\", \"justify-end\", \"uppercase\"], [1, \"font-bold\", \"text-lg\", \"uppercase\", \"my-1\", \"border-0\", \"bg-yellow-50\", \"pl-4\"], [1, \"my-1\", \"flex\", \"justify-between\", \"p-1\"], [1, \"text-left\", \"basis-[40%]\", \"border-2\", \"p-2\", \"rounded-md\"], [1, \"font-bold\", \"border-b-2\", \"uppercase\", \"p-2\"], [1, \"font-normal\"], [1, \"mt-2\"], [1, \"space-y-2\"], [1, \"uppercase\"], [1, \"font-bold\"], [1, \"h-[65vh]\", \"border-2\", \"p-2\", \"relative\"], [1, \"p-2\", \"font-bold\"], [1, \"flex\", \"gap-x-2\", \"gap-y-1\", \"bg-yellow-50\"], [1, \"basis-[20%]\"], [1, \"basis-[30%]\"], [1, \"basis-[10%]\"], [\"class\", \"basis-[10%]\", 4, \"ngIf\"], [\"class\", \"p-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\"], [1, \"text-center\", \"mt-4\"], [1, \"text-center\", \"mt-1\"], [1, \"p-2\"], [1, \"flex\", \"gap-x-2\", \"gap-y-1\"], [\"class\", \"flex justify-end p-4 absolute bottom-0\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\", \"p-4\", \"absolute\", \"bottom-0\"], [\"class\", \"bg-yellow-50 px-4 py-2 w-fit\", 4, \"ngIf\"], [1, \"bg-yellow-50\", \"px-4\", \"py-2\", \"w-fit\"], [1, \"font-bold\", \"mr-3\", \"uppercase\"]],\n  template: function ReceptionPrintComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtemplate(2, ReceptionPrintComponent_div_2_Template, 68, 20, \"div\", 2);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"id\", ctx.code);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.Object.keys(ctx.pages));\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe],\n  styles: [\"@page {\\n  size: A4 protrait;\\n  margin: 0mm;\\n}\\n.next-page[_ngcontent-%COMP%] {\\n  page-break-after: always !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbWV0aWVyL2VycC9tb2R1bGVzL2FjaGF0L2NvbXBvc2FudHMvcmVjZXB0aW9uLXByaW50L3JlY2VwdGlvbi1wcmludC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGlCQUFBO0VBQ0EsV0FBQTtBQUNGO0FBRUE7RUFDRSxtQ0FBQTtBQUFGIiwic291cmNlc0NvbnRlbnQiOlsiQHBhZ2Uge1xuICBzaXplOiBBNCBwcm90cmFpdDtcbiAgbWFyZ2luOiAwbW07XG59XG5cbi5uZXh0LXBhZ2Uge1xuICBwYWdlLWJyZWFrLWFmdGVyOiBhbHdheXMgIWltcG9ydGFudDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n});", "map": {"version": 3, "mappings": "AACA,SAASA,MAAM,QAAQ,MAAM;AAE7B,SAASC,SAAS,QAAQ,mCAAmC;;;;;;ICyDjDC,+BAA2C;IACzCA,0BACF;IAAAA,iBAAM;;;;;IACNA,+BAA4C;IAAAA,mBAAG;IAAAA,iBAAM;;;;;IACrDA,+BAA2C;IACzCA,2BACF;IAAAA,iBAAM;;;;;IAcNA,+BAA2C;IACzCA,YACF;;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,2IACF;;;;;;;;IACAA,+BAA2C;IACzCA,YACF;IAAAA,iBAAM;;;;;IADJA,eACF;IADEA,2TACF;;;;;IACAA,+BAA2C;IACzCA,YACF;;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,kMACF;;;;;IAGAA,+BAA4E;IACpCA,YAAgF;;IAAAA,iBAAM;IAC5HA,+BAAsC;IAAAA,YAAmF;;IAAAA,iBAAM;;;;;IADzFA,eAAgF;IAAhFA,uIAAgF;IAChFA,eAAmF;IAAnFA,0IAAmF;;;;;IAH7HA,+BAAsE;IACpEA,8FAGM;IACRA,iBAAM;;;;IAJuCA,eAAuB;IAAvBA,+CAAuB;;;;;IAtBtEA,+BAA2D;IAGrDA,YACF;IAAAA,iBAAM;IACNA,+BAAyB;IACvBA,YACF;IAAAA,iBAAM;IACNA,+BAAyB;IACvBA,YACF;IAAAA,iBAAM;IACNA,sFAEM;IACNA,sFAEM;IACNA,wFAEM;IACRA,iBAAM;IACNA,wFAKM;IACRA,iBAAM;;;;;IAxBAA,eACF;IADEA,4FACF;IAEEA,eACF;IADEA,+FACF;IAEEA,eACF;IADEA,mFACF;IAC0BA,eAAe;IAAfA,uCAAe;IAGfA,eAAe;IAAfA,uCAAe;IAGfA,eAAe;IAAfA,uCAAe;IAIUA,eAAe;IAAfA,uCAAe;;;;;IAkBlEA,+BAA2C;IACzCA,YACF;;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,qJACF;;;;;IACAA,+BAA2C;IACzCA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,iVACF;;;;;IACAA,+BAA2C;IACzCA,YACF;;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,sNACF;;;;;IAGAA,+BAA4E;IACpCA,YAAgF;;IAAAA,iBAAM;IAC5HA,+BAAsC;IAAAA,YAAmF;;IAAAA,iBAAM;;;;;IADzFA,eAAgF;IAAhFA,uIAAgF;IAChFA,eAAmF;IAAnFA,0IAAmF;;;;;IAH7HA,+BAAsE;IACpEA,8FAGM;IACRA,iBAAM;;;;IAJuCA,eAAuB;IAAvBA,+CAAuB;;;;;IAtBtEA,+BAA0D;IAGpDA,YACF;IAAAA,iBAAM;IACNA,+BAAyB;IACvBA,YACF;IAAAA,iBAAM;IACNA,+BAAyB;IACvBA,YACF;IAAAA,iBAAM;IACNA,sFAEM;IACNA,sFAEM;IACNA,wFAEM;IACRA,iBAAM;IACNA,wFAKM;IACRA,iBAAM;;;;IAxBAA,eACF;IADEA,oGACF;IAEEA,eACF;IADEA,uGACF;IAEEA,eACF;IADEA,2FACF;IAC0BA,eAAe;IAAfA,uCAAe;IAGfA,eAAe;IAAfA,uCAAe;IAGfA,eAAe;IAAfA,uCAAe;IAIUA,eAAe;IAAfA,uCAAe;;;;;IAnH1EA,8BAA0F;IAKhFA,yBAA0C;IAC5CA,iBAAM;IAGVA,8BAA6C;IAC1CA,YACH;;IAAAA,iBAAM;IAERA,8BAAyE;IACvEA,aACF;IAAAA,iBAAM;IACNA,gCAA2C;IAESA,+BAAc;IAAAA,iCAA0B;IAAAA,aAClF;IAAAA,iBAAO;IACbA,gCAAkB;IAGWA,uBAAM;IAAAA,iCAA0B;IAAAA,aAA2B;IAAAA,iBAAO;IACzFA,gCAAuB;IAAAA,2BAAU;IAAAA,iCAA0B;IAAAA,aAAmC;IAAAA,iBAAO;IAErGA,gCAAuB;IAAAA,uCAAY;IAAAA,iCAA0B;IAAAA,aAAkC;IAAAA,iBAAO;IAM9GA,gCAA2D;IACTA,0BAAS;IAAAA,iCAA0B;IAAAA,aAA0B;IAAAA,iBAAO;IACpHA,gCAAkB;IAGWA,6BAAY;IAAAA,iCAA0B;IAAAA,aAAyB;IAAAA,iBAAO;IAC7FA,gCAAuB;IAAAA,uCAAY;IAAAA,iCAA0B;IAAAA,aAA0B;IAAAA,iBAAO;IAOxGA,gCAA4C;IAIpCA,sCACF;IAAAA,iBAAM;IACNA,gCAAyB;IACvBA,mCACF;IAAAA,iBAAM;IACNA,gCAAyB;IACvBA,gCACF;IAAAA,iBAAM;IACNA,iFAEM;IACNA,iFAAqD;IACrDA,iFAEM;IACRA,iBAAM;IAERA,kFA2BM;IACNA,kFA2BM;IACRA,iBAAM;IACNA,gCAA8B;IAAAA,aAAqC;IAAAA,iBAAM;IACzEA,gCAA8B;IAC5BA,oFACF;IAAAA,iBAAM;IACNA,gCAA8B;IAAAA,wFAAuE;IAAAA,iBAAM;;;;;IArHtGA,eACH;IADGA,yFACH;IAGAA,eACF;IADEA,8IACF;IAG4FA,eAClF;IADkFA,sFAClF;IAIuDA,eAA2B;IAA3BA,oHAA2B;IACvBA,eAAmC;IAAnCA,mIAAmC;IAEjCA,eAAkC;IAAlCA,kIAAkC;IAOlBA,eAA0B;IAA1BA,wFAA0B;IAI1CA,eAAyB;IAAzBA,uFAAyB;IACzBA,eAA0B;IAA1BA,wFAA0B;IAmBjEA,gBAAe;IAAfA,uCAAe;IAGdA,eAAe;IAAfA,uCAAe;IAChBA,eAAe;IAAfA,uCAAe;IAKzBA,eAAyB;IAAzBA,0DAAyB;IA4BvCA,eAAyB;IAAzBA,gDAAyB;IA6BHA,eAAqC;IAArCA,wEAAqC;;;ADpHzE,OAAM,MAAOC,uBAAwB,SAAQF,SAAS;EASpDG,YAA+BC,GAAe;IAC5C,KAAK,CAACA,GAAG,CAAC;IADmB,QAAG,GAAHA,GAAG;IARlC,SAAI,GAAG,SAAS;IAChB,QAAG,GAAQ,EAAE;IACb,UAAK,GAAQ,EAAE;IACf,YAAO,GAAW,CAAC;IACnB,YAAO,GAAU,EAAE;IACnB,QAAG,GAAQ,EAAE;IACb,cAAS,GAAY,KAAK;EAI1B;EAEAC,QAAQ;IACN,IAAI,CAACD,GAAG,CACLE,cAAc;IACf;IAAA,CACCC,IAAI,CACHR,MAAM,CAAES,SAAS,IAAI;MACnB,OAAOA,SAAS,EAAEC,IAAI,IAAI,IAAI,CAACA,IAAI;IACrC,CAAC,CAAC,CACH,CACAC,SAAS,CAAEF,SAAS,IAAI;MACvB,IAAI,CAACA,SAAS,CAACG,QAAQ,EAAE;QACvB,IAAI,CAACC,SAAS,GAAGJ,SAAS,EAAEK,OAAO;QACnC,IAAI,CAACC,GAAG,GAAGN,SAAS,CAACO,IAAI,EAAED,GAAG;QAC9B,IAAI,CAACE,KAAK,GAAG,IAAI,CAACC,aAAa,CAACT,SAAS,CAACO,IAAI,EAAEG,OAAO,CAAC;QACxD,IAAI,CAACA,OAAO,GAAGV,SAAS,CAACO,IAAI,EAAEG,OAAO;QACtC,IAAI,CAACC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACL,KAAK,CAAC,CAACM,MAAM;QAC7C,IAAG,IAAI,CAACR,GAAG,CAACS,UAAU,EAAC;UACrB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,WAAW,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAChDC,EAAO,IAAKA,EAAE,CAACC,UAAU,IAAI,IAAI,CAACd,GAAG,CAACS,UAAU,CAACd,IAAI,CACvD;;QAEHoB,UAAU,CAAC,MAAK;UACd,IAAGrB,SAAS,CAACG,QAAQ,EAAE;UACvB,IAAI,CAACmB,OAAO,CAAC,IAAI,CAACrB,IAAI,CAAC;UACvBD,SAAS,CAACG,QAAQ,GAAG,IAAI;QAC3B,CAAC,EAAE,GAAG,CAAC;;IAEX,CAAC,CAAC;EACN;EAEAM,aAAa,CAACc,GAAU;IACtB,IAAIC,MAAM,GAAU,EAAE;IACtB,MAAMC,aAAa,GAAG,CACpB,GAAG,IAAIC,GAAG,CACRH,GAAG,CAACI,GAAG,CAAEJ,GAAG,IAAI;MACd,MAAMK,GAAG,GAAYL,GAAG,CAACM,YAAY,CAACC,OAAQ,EAAEC,KAAK,CAAC,GAAG,CAAC;MAC1D,OAAOH,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;IACrB,CAAC,CAAC,CACH,CACF;IACDH,aAAa,CAACO,OAAO,CAAEJ,GAAW,IAAI;MACpC,MAAMK,KAAK,GAAQ;QAAEL,GAAG;QAAEM,GAAG,EAAE;MAAK,CAAE;MACtC,MAAMJ,OAAO,GAAG,IAAI,CAACb,WAAW,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAC5DiB,CAAC,IAAKA,CAAC,CAAClC,IAAI,IAAI2B,GAAG,CACrB;MACD,IAAIE,OAAO,EAAE;QACXG,KAAK,CAACG,OAAO,GAAGN,OAAO,CAACM,OAAO;;MAEjC;MACAZ,MAAM,GAAGA,MAAM,CAACa,MAAM,CACpBd,GAAG,CAAChC,MAAM,CAAE+C,EAAE,IACHA,EAAE,CAACT,YAAY,CAACC,OAAQ,CAACS,QAAQ,CAAC,IAAIX,GAAG,EAAE,CAAC,CACtD,CACF;IACH,CAAC,CAAC;IACF;IACA,MAAMY,cAAc,GAAG,EAAE;IACzB,MAAMhC,KAAK,GAAQ,EAAE;IACrB;IACA,IAAIiC,WAAW,GAAG,CAAC;IACnB,IAAIC,OAAO,GAAG,CAAC;IACflB,MAAM,CAACQ,OAAO,CAAC,CAACM,EAAE,EAAEK,GAAG,KAAI;MACzB,IAAID,OAAO,GAAGF,cAAc,EAAE;QAC5BC,WAAW,IAAI,CAAC;QAChBC,OAAO,GAAG,CAAC;;MAEb,IAAI,CAAClC,KAAK,CAACiC,WAAW,CAAC,EAAE;QACvBjC,KAAK,CAACiC,WAAW,CAAC,GAAG;UAAEG,MAAM,EAAE;QAAE,CAAE;;MAG7BpC,KAAK,CAACiC,WAAW,CAAC,CAACG,MAAO,CAACC,IAAI,CAACP,EAAE,CAAC;MAC3CI,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IACF,OAAOlC,KAAK;EACd;EAEAsC,cAAc;IACZ,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,GAAG,GAAQ,EAAE;IACjB,IAAI,CAAChC,WAAW,CAAC,kBAAkB,CAAC,CAACe,OAAO,CAAEkB,EAAE,IAAI;MAClDD,GAAG,CAACC,EAAE,CAACC,GAAG,CAAC,GAAG,CAAC;IACjB,CAAC,CAAC;IACF,KAAK,IAAIb,EAAE,IAAI,IAAI,CAAC5B,OAAO,EAAE;MAC3BsC,EAAE,IAAIV,EAAE,CAACc,YAAY,CAACC,OAAO,GAAGf,EAAE,CAACgB,QAAQ,CAACC,GAAG;MAC/CR,GAAG,IAAKT,EAAE,CAACc,YAAY,CAACC,OAAO,GAAGf,EAAE,CAACgB,QAAQ,CAACC,GAAG,GAAKjB,EAAE,CAACc,YAAY,CAACH,GAAI;MAC1EA,GAAG,CAAEX,EAAE,CAACc,YAAY,CAACH,GAAG,CAAE,IAAMX,EAAE,CAACc,YAAY,CAACC,OAAO,GAAGf,EAAE,CAACgB,QAAQ,CAACC,GAAG,GAAKjB,EAAE,CAACc,YAAY,CAACH,GAAI,GAAKX,EAAE,CAACc,YAAY,CAACC,OAAO,GAAGf,EAAE,CAACgB,QAAQ,CAACE,GAAI;;IAEnJ,IAAI,CAACvC,WAAW,CAAC,kBAAkB,CAAC,CAACe,OAAO,CAAEkB,EAAE,IAAI;MAClD,IAAID,GAAG,CAACC,EAAE,CAACC,GAAG,CAAC,IAAI,CAAC,EAAE;QACpB,OAAOF,GAAG,CAACC,EAAE,CAACC,GAAG,CAAC;;IAEtB,CAAC,CAAC;IACF,OAAO;MAAEJ,GAAG;MAAEC,EAAE;MAAEC;IAAG,CAAE;EAEzB;EAEAQ,aAAa,CAACnD,GAAQ;IACpB,IAAGA,GAAG,IAAIA,GAAG,CAACoD,QAAQ,IAAIpD,GAAG,CAACqD,cAAc,EAAC;MAC3C,OAAOrD,GAAG,CAACqD,cAAc,CAAC5B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEzC,OAAOzB,GAAG,CAACqD,cAAc;EAC3B;;AAnHWjE,uBAAwB;mBAAxBA,uBAAuB;AAAA;AAAvBA,uBAAwB;QAAxBA,uBAAuB;EAAAkE;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCTpCxE,8BAAoB;MAEhBA,0EAgIM;MACRA,iBAAM;;;MAlIDA,eAAW;MAAXA,6BAAW;MACYA,eAAqB;MAArBA,oDAAqB", "names": ["filter", "Composant", "i0", "ReceptionPrintComponent", "constructor", "app", "ngOnInit", "getPrintStream", "pipe", "printData", "code", "subscribe", "consumed", "with<PERSON><PERSON>", "checked", "doc", "data", "pages", "gerenatePages", "details", "nbPages", "Object", "keys", "length", "data_tiers", "cls", "getDataList", "find", "ob", "code_tiers", "setTimeout", "appercu", "det", "result", "listeFamilles", "Set", "map", "fam", "data_produit", "famille", "split", "for<PERSON>ach", "lgFam", "tpe", "f", "libelle", "concat", "lg", "endsWith", "nbLigneParPage", "pageEnCours", "nbLigne", "ndx", "lignes", "push", "calculateTotal", "ttc", "ht", "tva", "tv", "val", "data_pricing", "prix_ht", "data_qte", "abr", "vcc", "get_num_recep", "is_avoir", "numero_interne", "selectors", "features", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\achat\\composants\\reception-print\\reception-print.component.ts", "G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\achat\\composants\\reception-print\\reception-print.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { filter } from 'rxjs';\nimport { AppService } from 'src/app/core/services/app.service';\nimport { Composant } from 'src/app/core/types/composant.base';\n\n@Component({\n  selector: 'reception-print',\n  templateUrl: './reception-print.component.html',\n  styleUrls: ['./reception-print.component.scss'],\n})\nexport class ReceptionPrintComponent extends Composant implements OnInit {\n  code = 'page_rc';\n  doc: any = {};\n  pages: any = {};\n  nbPages: number = 1;\n  details: any[] = [];\n  cls: any = {};\n  withPrice: boolean = false\n\n  constructor(protected override app: AppService) {\n    super(app);\n  }\n\n  ngOnInit(): void {\n    this.app\n      .getPrintStream()\n      // .asObservable()\n      .pipe(\n        filter((printData) => {\n          return printData?.code == this.code;\n        })\n      )\n      .subscribe((printData) => {\n        if (!printData.consumed) {\n          this.withPrice = printData?.checked\n          this.doc = printData.data?.doc;\n          this.pages = this.gerenatePages(printData.data?.details);\n          this.details = printData.data?.details\n          this.nbPages = Object.keys(this.pages).length;\n          if(this.doc.data_tiers){\n            this.cls = this.getDataList(`tiers.liste_frs`).find(\n              (ob: any) => ob.code_tiers == this.doc.data_tiers.code\n            );\n          }\n          setTimeout(() => {\n            if(printData.consumed) return\n            this.appercu(this.code);\n            printData.consumed = true;\n          }, 300);\n        }\n      });\n  }\n\n  gerenatePages(det: any[]) {\n    let result: any[] = [];\n    const listeFamilles = [\n      ...new Set(\n        det.map((det) => {\n          const fam = (<string>det.data_produit.famille)?.split('.');\n          return fam[1] || '';\n        })\n      ),\n    ];\n    listeFamilles.forEach((fam: string) => {\n      const lgFam: any = { fam, tpe: 'fam' };\n      const famille = this.getDataList('product.liste_familles').find(\n        (f) => f.code == fam\n      );\n      if (famille) {\n        lgFam.libelle = famille.libelle;\n      }\n      // result.push(lgFam);\n      result = result.concat(\n        det.filter((lg) =>\n          (<string>lg.data_produit.famille).endsWith(`.${fam}`)\n        )\n      );\n    });\n    //18\n    const nbLigneParPage = 13;\n    const pages: any = {};\n    // const nbPage = Math.ceil(result.length / nbLigneParPage);\n    let pageEnCours = 1;\n    let nbLigne = 0;\n    result.forEach((lg, ndx) => {\n      if (nbLigne > nbLigneParPage) {\n        pageEnCours += 1;\n        nbLigne = 0;\n      }\n      if (!pages[pageEnCours]) {\n        pages[pageEnCours] = { lignes: [] };\n      }\n\n      (<any[]>pages[pageEnCours].lignes).push(lg);\n      nbLigne += 1;\n    });\n    return pages;\n  }\n\n  calculateTotal(): any {\n    let ttc = 0;\n    let ht = 0;\n    let tva: any = {}\n    this.getDataList(\"commun.liste_tva\").forEach((tv) => {\n      tva[tv.val] = 0\n    })\n    for (let lg of this.details) {\n      ht += lg.data_pricing.prix_ht * lg.data_qte.abr;\n      ttc += (lg.data_pricing.prix_ht * lg.data_qte.abr) * (lg.data_pricing.tva);\n      tva[(lg.data_pricing.tva)] += ((lg.data_pricing.prix_ht * lg.data_qte.abr) * (lg.data_pricing.tva)) - (lg.data_pricing.prix_ht * lg.data_qte.vcc)\n    }\n    this.getDataList(\"commun.liste_tva\").forEach((tv) => {\n      if (tva[tv.val] == 0) {\n        delete tva[tv.val]\n      }\n    })\n    return { ttc, ht, tva };\n\n  }\n\n  get_num_recep(doc: any){\n    if(doc && doc.is_avoir && doc.numero_interne){\n      return doc.numero_interne.split('F')[1]\n    }\n    return doc.numero_interne\n  }\n}\n", "\n<div class=\"hidden\">\n  <div [id]=\"code\">\n    <div *ngFor=\"let num_page of Object.keys(pages)\" class=\"next-page relative h-[100vh] p-2\">\n      <div class=\"overflow-hidden flex items-end p-2 border-0\">\n        <div class=\"basis-[90%]\">\n          <div class=\"w-[50%] flex justify-center\">\n            <div>\n              <img src=\"./assets/logo-top-petrol.png\" />\n            </div>\n          </div>\n        </div>\n        <div class=\"pr-2 flex justify-end uppercase\">\n           {{ doc.date_doc | date : \"dd-MM-yyyy\" }}\n        </div>\n      </div>\n      <div class=\"font-bold text-lg uppercase my-1 border-0 bg-yellow-50 pl-4\">\n        {{doc.is_avoir ? 'Avoir fournisseur ' : 'Bon de Réception N° '}} {{ doc.numero_interne }}\n      </div>\n      <div class=\"my-1 flex justify-between p-1\">\n        <div class=\"text-left basis-[40%] border-2 p-2 rounded-md\">\n          <div class=\"font-bold border-b-2 uppercase p-2\">Fournisseur : <span class=\"font-normal\">{{ doc.data_tiers?.nom\n              }}</span></div>\n          <div class=\"mt-2\">\n            <div class=\"space-y-2\">\n              <div class=\"uppercase\">\n                <div class=\"font-bold\">ice : <span class=\"font-normal\">{{ cls?.data_statut?.ice }}</span> </div>\n                <div class=\"font-bold\">Adresse : <span class=\"font-normal\">{{ cls?.data_adresses['f']?.adre }}</span>\n                </div>\n                <div class=\"font-bold\">téléphone : <span class=\"font-normal\">{{ cls?.data_adresses['f']?.tel }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"text-left basis-[40%] border-2 p-2 rounded-md\">\n          <div class=\"font-bold border-b-2 uppercase p-2\">Camion : <span class=\"font-normal\">{{ doc.data_camion?.mat }}</span></div>      \n          <div class=\"mt-2\">\n            <div class=\"space-y-2\">\n              <div class=\"uppercase\">\n                <div class=\"font-bold\">Chauffeur : <span class=\"font-normal\">{{ doc.data_camion?.ch }}</span> </div>\n                <div class=\"font-bold\">téléphone : <span class=\"font-normal\">{{ doc.data_camion?.tel }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"h-[65vh] border-2 p-2 relative\">\n        <div class=\"p-2 font-bold\">\n          <div class=\"flex gap-x-2 gap-y-1 bg-yellow-50\">\n            <div class=\"basis-[20%]\">\n              Référence\n            </div>\n            <div class=\"basis-[30%]\">\n              Désignation\n            </div>\n            <div class=\"basis-[10%]\">\n              Quantité\n            </div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              Prix TTC\n            </div>\n            <div class=\"basis-[10%]\"  *ngIf=\"withPrice\">TVA</div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              Total TTC\n            </div>\n          </div>\n        </div>\n        <div *ngFor=\"let lg of pages[num_page].lignes\" class=\"p-2\">\n          <div class=\"flex gap-x-2 gap-y-1\">\n            <div class=\"basis-[20%]\">\n              {{ lg.data_produit?.code }}\n            </div>\n            <div class=\"basis-[30%]\">\n              {{ lg.data_produit?.libelle }}\n            </div>\n            <div class=\"basis-[10%]\">\n              {{ lg.data_qte?.abr }}\n            </div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              {{ lg.data_pricing?.prix_ttc || 0 | number : \".2-2\" : \"fr\" }}\n            </div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              {{ getDataList(\"commun.liste_tva\", ['val','==',lg.data_pricing?.tva])[0]?.label }}\n            </div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              {{ lg.data_pricing?.prix_ttc*lg.data_qte?.abr || 0 | number : \".2-2\" : \"fr\" }}\n            </div>\n          </div>\n          <div class=\"flex justify-end p-4 absolute bottom-0\" *ngIf=\"withPrice\">\n            <div class=\"bg-yellow-50 px-4 py-2 w-fit\" *ngIf=\"doc.data_valeurs as total\">\n              <div class=\"font-bold mr-3 uppercase\">Total HT : {{ doc.is_avoir?- total.mt_ht : total.mt_ht| number : \".2-2\" : \"fr\"}}</div>\n              <div class=\"font-bold mr-3 uppercase\">Total TTC : {{ doc.is_avoir?- total.mt_ttc: total.mt_ttc | number : \".2-2\" : \"fr\"}}</div>\n            </div>\n          </div>\n        </div>\n        <div *ngIf=\"doc.data_frais.lst as listeFrais\" class=\"p-2\">\n          <div class=\"flex gap-x-2 gap-y-1\">\n            <div class=\"basis-[20%]\">\n              {{ lg.data_produit?.code }}\n            </div>\n            <div class=\"basis-[30%]\">\n              {{ lg.data_produit?.libelle }}\n            </div>\n            <div class=\"basis-[10%]\">\n              {{ lg.data_qte?.abr }}\n            </div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              {{ lg.data_pricing?.prix_ttc || 0 | number : \".2-2\" : \"fr\" }}\n            </div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              {{ getDataList(\"commun.liste_tva\", ['val','==',lg.data_pricing?.tva])[0]?.label }}\n            </div>\n            <div class=\"basis-[10%]\" *ngIf=\"withPrice\">\n              {{ lg.data_pricing?.prix_ttc*lg.data_qte?.abr || 0 | number : \".2-2\" : \"fr\" }}\n            </div>\n          </div>\n          <div class=\"flex justify-end p-4 absolute bottom-0\" *ngIf=\"withPrice\">\n            <div class=\"bg-yellow-50 px-4 py-2 w-fit\" *ngIf=\"doc.data_valeurs as total\">\n              <div class=\"font-bold mr-3 uppercase\">Total HT : {{ doc.is_avoir?- total.mt_ht : total.mt_ht| number : \".2-2\" : \"fr\"}}</div>\n              <div class=\"font-bold mr-3 uppercase\">Total TTC : {{ doc.is_avoir?- total.mt_ttc: total.mt_ttc | number : \".2-2\" : \"fr\"}}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"flex justify-end\">Page : {{ num_page }} / {{ nbPages }}</div>\n      <div class=\"text-center mt-4\">\n        TOP PETROL - BUSINESS CENTRE YASMINA 1ER ETG AIN SEBAA CASABLANCA\n      </div>\n      <div class=\"text-center mt-1\">IF:1687898 - ICE: 000164488000066 - Patente: 31990429 - FIX: 0522343920</div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}