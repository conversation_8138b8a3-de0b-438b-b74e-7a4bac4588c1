<div class="flex justify-between gap-4 pt-4">
    <div></div>
    <form-item label="Recherche (Intitulé ou montant ou numero de cheque)">
        <input nzAllowClear nz-input [(ngModel)]="filtre.sh" class="w-[30rem]" 
            (keyup.enter)="this.setDataString('bq.intitule', filtre.sh);
            traiteEcritureBanque(Actions.Get,true,journal,{ annee:year, sh:filtre.sh},isValid)" />
    </form-item>
    <div class="">
        <form-item label="Opérations validées">
            <nz-switch [(ngModel)]="isValid" [nzDisabled]="!mois" [nzControl]="true"
                (click)="isValid=!isValid"></nz-switch>
        </form-item>
    </div>
</div>
<div class="flex gap-2">
    <div class="w-[50%]">
        <div class="py-2 font-bold">Entrées en banque</div>
        <nz-table nzSize="small" [nzFrontPagination]="true" class="h-[55vh] overflow-y-auto" #tableListeEntree
            [nzData]="getListEcritureEntree()">
            <thead>
                <tr>
                    <th nzWidth="20%">Dates</th>
                    <th nzWidth="20%">Tiers</th>
                    <th>Opération</th>
                    <th nzWidth="10%">Montant</th>
                    <th nzWidth="20%"></th>
                </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let ecr of tableListeEntree.data">
                    <tr>
                        <td>
                            {{ ecr.date_echeance }}
                        </td>
                        <td>
                            {{ ecr.data_tiers?.nom }}
                        </td>
                        <td>
                            {{ ecr.tpe_doc | lbl : 'commun.liste_operation_ca_bq' : 'code' : 'label' }}
                        </td>
                        <td>
                            {{ ecr.total_credit > 0 ? (ecr.total_credit | number : '.2-2') : '' }}
                        </td>
                        <td class="flex">
                            <a nz-button [disabled]="!financialPrivilege()"
                                (click)="loadEcriture('bq',ecr.id, 'form_banque',{journal,mois,isSuivis:true})"
                                class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600"
                                nz-tooltip nzTooltipTitle="Modifier">
                                <span nz-icon nzType="edit" nzTheme="outline"></span>
                            </a>
                            <a nz-button [disabled]="!adminPrivilege()" nz-popconfirm
                                (nzOnConfirm)="adminPrivilege() && majEspece(Actions.Del, ecr, mois, true)"
                                [nzPopconfirmTitle]="Messages.confirm_delete" nzPopconfirmPlacement="bottom"
                                class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-red-500 hover:text-red-600"
                                nz-tooltip nzTooltipTitle="Supprimer">
                                <span nz-icon nzType="delete" nzTheme="outline"></span>
                            </a>
                            <a *ngIf="ecr.data_etat.etat == 'sent'" nz-button [disabled]="!financialPrivilege()"
                                nz-popconfirm
                                (nzOnConfirm)="ecr.new_etat = 'inpaye'; majEtatEcritureBq(true, mois,filtre.sh, ecr)"
                                nzPopconfirmTitle="Voulez-vous changer l'état de cette écriture ?"
                                nzPopconfirmPlacement="bottom"
                                class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600"
                                nz-tooltip nzTooltipTitle="Chèque impayé">
                                <span nz-icon nzType="rollback" nzTheme="outline"></span>
                            </a>
                            <div>
                                <a nz-button *ngIf="ecr.data_etat.etat == 'sent'" [disabled]="!financialPrivilege()"
                                    nz-popover nzPopoverPlacement="bottom" [nzPopoverContent]="formValidation"
                                    nzPopoverTrigger="click" nzPopoverPlacement="bottom"
                                    [nzPopoverContent]="formValidation"
                                    class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600"
                                    nz-tooltip nzTooltipTitle="Valider l'écriture?">
                                    <span nz-icon nzType="check-circle" nzTheme="outline"></span>
                                </a>
                                <ng-template #formValidation>
                                    <div class="flex">
                                        <form-item label="Date de validation">
                                            <nz-date-picker class="w-full"
                                                [(ngModel)]="ecr.date_validation"></nz-date-picker>
                                        </form-item>
                                    </div>
                                    <div class="pt-3 text-right">
                                        <button [disabled]="!ecr.date_validation" nz-button
                                            (click)="ecr.new_etat = 'encaisse'; majEtatEcritureBq(true, mois,filtre.sh, ecr)">Valider</button>
                                    </div>
                                </ng-template>
                            </div>
                            <a *ngIf="ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye'"
                                [disabled]="!financialPrivilege()"
                                class="cursor-pointer flex justify-center items-center border p-2 duration-200"
                                [class]="ecr.data_etat.etat == 'draft' ? 'text-blue-500 hover:border-blue-500' : 'text-red-500 hover:border-red-500'"
                                nz-button nz-popconfirm nz-tooltip
                                [nzTooltipTitle]="(ecr.data_etat.etat == 'draft' && 'Émettre à la banque') || (ecr.data_etat.etat == 'inpaye' && 'Renvoyer a la banque') || ''"
                                (nzOnConfirm)="
                                    (ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye')
                                        ? (ecr.new_etat = 'sent')
                                        : ecr.data_etat.etat == 'sent'
                                        ? (ecr.new_etat = 'encaisse')
                                        : '';
                                    majEtatEcritureBq(true, mois,filtre.sh, ecr)
                                " nzPopconfirmTitle="Voulez-vous changer l'état de cette écriture ?"
                                nzPopconfirmPlacement="bottom">
                                <span nz-icon
                                    [nzType]="(ecr.data_etat.etat == 'draft' && 'bank') || (ecr.data_etat.etat == 'inpaye' && 'issues-close') || ''"
                                    nzTheme="outline"></span>
                            </a>
                        </td>
                    </tr>
                </ng-container>
            </tbody>
        </nz-table>
    </div>
    <div class="w-[50%]">
        <div class="py-2 font-bold">Sorties</div>
        <nz-table nzSize="small" class="h-[55vh] overflow-y-auto" #tableListeSortie [nzData]="getListEcritureSortie()"
            [nzFrontPagination]="true">
            <thead>
                <tr>
                    <th nzWidth="20%">Date</th>
                    <th nzWidth="20%">Tiers</th>
                    <th>Opération</th>
                    <th nzWidth="10%">Montant</th>
                    <th nzWidth="15%"></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let ecr of tableListeSortie.data">
                    <td>
                        {{ ecr.date_echeance | date: 'dd/MM/yyyy' }}
                    </td>
                    <td class="text-right">
                        {{ ecr.data_tiers?.nom }}
                    </td>
                    <td class="text-right">
                        {{ ecr.tpe_doc | lbl : 'commun.liste_operation_ca_bq' : 'code' : 'label' }}
                    </td>
                    <td>
                        {{ ecr.total_debit > 0 ? (ecr.total_debit | number : '.2-2') : '' }}
                    </td>
                    <td class="flex">
                        <a nz-button [disabled]="!financialPrivilege()"
                            (click)="loadEcriture('bq',ecr.id, 'form_banque',{journal,mois,isSuivis:true})"
                            class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600"
                            nz-tooltip nzTooltipTitle="Modifier">
                            <span nz-icon nzType="edit" nzTheme="outline"></span>
                        </a>
                        <a nz-button [disabled]="!adminPrivilege()" nz-popconfirm
                            (nzOnConfirm)="adminPrivilege() && majEspece(Actions.Del, ecr, mois, true)"
                            [nzPopconfirmTitle]="Messages.confirm_delete" nzPopconfirmPlacement="bottom"
                            class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-red-500 hover:text-red-600"
                            nz-tooltip nzTooltipTitle="Supprimer">
                            <span nz-icon nzType="delete" nzTheme="outline"></span>
                        </a>
                        <a *ngIf="ecr.data_etat.etat == 'sent'" nz-button [disabled]="!financialPrivilege()"
                            nz-popconfirm
                            (nzOnConfirm)="ecr.new_etat = 'inpaye'; majEtatEcritureBq(true, mois,filtre.sh, ecr)"
                            nzPopconfirmTitle="Voulez-vous changer l'état de cette écriture ?"
                            nzPopconfirmPlacement="bottom"
                            class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600"
                            nz-tooltip nzTooltipTitle="Chèque impayé">
                            <span nz-icon nzType="rollback" nzTheme="outline"></span>
                        </a>
                        <div>
                            <a nz-button *ngIf="ecr.data_etat.etat == 'sent'" [disabled]="!financialPrivilege()"
                                nz-popover nzPopoverPlacement="bottom" [nzPopoverContent]="formValidation"
                                nzPopoverTrigger="click" nzPopoverPlacement="bottom" [nzPopoverContent]="formValidation"
                                class="cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600"
                                nz-tooltip nzTooltipTitle="Valider l'écriture?">
                                <span nz-icon nzType="check-circle" nzTheme="outline"></span>
                            </a>
                            <ng-template #formValidation>
                                <div class="flex">
                                    <form-item label="Date de validation">
                                        <nz-date-picker class="w-full"
                                            [(ngModel)]="ecr.date_validation"></nz-date-picker>
                                    </form-item>
                                </div>
                                <div class="pt-3 text-right">
                                    <button nz-button [disabled]="!ecr.date_validation"
                                        (click)="ecr.new_etat = 'encaisse'; majEtatEcritureBq(true, mois,filtre.sh, ecr)">Valider</button>
                                </div>
                            </ng-template>
                        </div>
                        <a *ngIf="ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye'"
                            [disabled]="!financialPrivilege()"
                            class="cursor-pointer flex justify-center items-center border p-2 duration-200"
                            [class]="ecr.data_etat.etat == 'draft' ? 'text-blue-500 hover:border-blue-500' : 'text-red-500 hover:border-red-500'"
                            nz-button nz-popconfirm nz-tooltip
                            [nzTooltipTitle]="(ecr.data_etat.etat == 'draft' && 'Émettre à la banque') || (ecr.data_etat.etat == 'inpaye' && 'Renvoyer a la banque') || ''"
                            (nzOnConfirm)="
                                (ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye')
                                    ? (ecr.new_etat = 'sent')
                                    : ecr.data_etat.etat == 'sent'
                                    ? (ecr.new_etat = 'encaisse')
                                    : '';
                                majEtatEcritureBq(true, mois,filtre.sh, ecr)
                            " nzPopconfirmTitle="Voulez-vous changer l'état de cette écriture ?"
                            nzPopconfirmPlacement="bottom">
                            <span nz-icon
                                [nzType]="(ecr.data_etat.etat == 'draft' && 'bank') || (ecr.data_etat.etat == 'inpaye' && 'issues-close') || ''"
                                nzTheme="outline"></span>
                        </a>
                    </td>
                </tr>
            </tbody>
        </nz-table>
    </div>
</div>