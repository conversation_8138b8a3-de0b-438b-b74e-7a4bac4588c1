{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { TresorComposant } from '../../features/tresor.composant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/app.service\";\nimport * as i2 from \"src/app/store/services/back-end.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"ng-zorro-antd/icon\";\nimport * as i6 from \"ng-zorro-antd/input\";\nimport * as i7 from \"ng-zorro-antd/popconfirm\";\nimport * as i8 from \"ng-zorro-antd/date-picker\";\nimport * as i9 from \"ng-zorro-antd/switch\";\nimport * as i10 from \"ng-zorro-antd/popover\";\nimport * as i11 from \"ng-zorro-antd/table\";\nimport * as i12 from \"ng-zorro-antd/button\";\nimport * as i13 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i14 from \"ng-zorro-antd/core/wave\";\nimport * as i15 from \"ng-zorro-antd/tooltip\";\nimport * as i16 from \"../../../../../../../../shared/composants/form-item/form-item.component\";\nimport * as i17 from \"../../../../../../../../shared/pipes/lab-by-id.pipe\";\nfunction BanqueSuiviComponent_ng_container_25_a_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"nzOnConfirm\", function BanqueSuiviComponent_ng_container_25_a_17_Template_a_nzOnConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ecr_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      ecr_r4.new_etat = \"inpaye\";\n      return i0.ɵɵresetView(ctx_r10.majEtatEcritureBq(true, ctx_r10.mois, ctx_r10.filtre.sh, ecr_r4));\n    });\n    i0.ɵɵelement(1, \"span\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.financialPrivilege());\n  }\n}\nfunction BanqueSuiviComponent_ng_container_25_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 28);\n    i0.ɵɵelement(1, \"span\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r7 = i0.ɵɵreference(21);\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r6.financialPrivilege())(\"nzPopoverContent\", _r7)(\"nzPopoverContent\", _r7);\n  }\n}\nfunction BanqueSuiviComponent_ng_container_25_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"form-item\", 30)(2, \"nz-date-picker\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function BanqueSuiviComponent_ng_container_25_ng_template_20_Template_nz_date_picker_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ecr_r4 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(ecr_r4.date_validation = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(3, \"div\", 32)(4, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function BanqueSuiviComponent_ng_container_25_ng_template_20_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ecr_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      ecr_r4.new_etat = \"encaisse\";\n      return i0.ɵɵresetView(ctx_r16.majEtatEcritureBq(true, ctx_r16.mois, ctx_r16.filtre.sh, ecr_r4));\n    });\n    i0.ɵɵtext(5, \"Valider\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ecr_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ecr_r4.date_validation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ecr_r4.date_validation);\n  }\n}\nfunction BanqueSuiviComponent_ng_container_25_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 34);\n    i0.ɵɵlistener(\"nzOnConfirm\", function BanqueSuiviComponent_ng_container_25_a_22_Template_a_nzOnConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ecr_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      ecr_r4.data_etat.etat == \"draft\" || ecr_r4.data_etat.etat == \"inpaye\" ? ecr_r4.new_etat = \"sent\" : ecr_r4.data_etat.etat == \"sent\" ? ecr_r4.new_etat = \"encaisse\" : \"\";\n      return i0.ɵɵresetView(ctx_r19.majEtatEcritureBq(true, ctx_r19.mois, ctx_r19.filtre.sh, ecr_r4));\n    });\n    i0.ɵɵelement(1, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ecr_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ecr_r4.data_etat.etat == \"draft\" ? \"text-blue-500 hover:border-blue-500\" : \"text-red-500 hover:border-red-500\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.financialPrivilege())(\"nzTooltipTitle\", ecr_r4.data_etat.etat == \"draft\" && \"\\u00C9mettre \\u00E0 la banque\" || ecr_r4.data_etat.etat == \"inpaye\" && \"Renvoyer a la banque\" || \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"nzType\", ecr_r4.data_etat.etat == \"draft\" && \"bank\" || ecr_r4.data_etat.etat == \"inpaye\" && \"issues-close\" || \"\");\n  }\n}\nfunction BanqueSuiviComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"lbl\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 17)(13, \"a\", 18);\n    i0.ɵɵlistener(\"click\", function BanqueSuiviComponent_ng_container_25_Template_a_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const ecr_r4 = restoredCtx.$implicit;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.loadEcriture(\"bq\", ecr_r4.id, \"form_banque\", {\n        journal: ctx_r23.journal,\n        mois: ctx_r23.mois,\n        isSuivis: true\n      }));\n    });\n    i0.ɵɵelement(14, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"a\", 20);\n    i0.ɵɵlistener(\"nzOnConfirm\", function BanqueSuiviComponent_ng_container_25_Template_a_nzOnConfirm_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const ecr_r4 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.adminPrivilege() && ctx_r25.majEspece(ctx_r25.Actions.Del, ecr_r4, ctx_r25.mois, true));\n    });\n    i0.ɵɵelement(16, \"span\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, BanqueSuiviComponent_ng_container_25_a_17_Template, 2, 1, \"a\", 22);\n    i0.ɵɵelementStart(18, \"div\");\n    i0.ɵɵtemplate(19, BanqueSuiviComponent_ng_container_25_a_19_Template, 2, 3, \"a\", 23);\n    i0.ɵɵtemplate(20, BanqueSuiviComponent_ng_container_25_ng_template_20_Template, 6, 2, \"ng-template\", null, 24, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, BanqueSuiviComponent_ng_container_25_a_22_Template, 2, 5, \"a\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ecr_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ecr_r4.date_echeance, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ecr_r4.data_tiers == null ? null : ecr_r4.data_tiers.nom, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(8, 10, ecr_r4.tpe_doc, \"commun.liste_operation_ca_bq\", \"code\", \"label\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ecr_r4.total_credit > 0 ? i0.ɵɵpipeBind2(11, 15, ecr_r4.total_credit, \".2-2\") : \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.financialPrivilege());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.adminPrivilege())(\"nzPopconfirmTitle\", ctx_r1.Messages.confirm_delete);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ecr_r4.data_etat.etat == \"sent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ecr_r4.data_etat.etat == \"sent\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ecr_r4.data_etat.etat == \"draft\" || ecr_r4.data_etat.etat == \"inpaye\");\n  }\n}\nfunction BanqueSuiviComponent_tr_43_a_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"nzOnConfirm\", function BanqueSuiviComponent_tr_43_a_17_Template_a_nzOnConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ecr_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r32 = i0.ɵɵnextContext();\n      ecr_r26.new_etat = \"inpaye\";\n      return i0.ɵɵresetView(ctx_r32.majEtatEcritureBq(true, ctx_r32.mois, ctx_r32.filtre.sh, ecr_r26));\n    });\n    i0.ɵɵelement(1, \"span\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r27.financialPrivilege());\n  }\n}\nfunction BanqueSuiviComponent_tr_43_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 28);\n    i0.ɵɵelement(1, \"span\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r29 = i0.ɵɵreference(21);\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r28.financialPrivilege())(\"nzPopoverContent\", _r29)(\"nzPopoverContent\", _r29);\n  }\n}\nfunction BanqueSuiviComponent_tr_43_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"form-item\", 30)(2, \"nz-date-picker\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function BanqueSuiviComponent_tr_43_ng_template_20_Template_nz_date_picker_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ecr_r26 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(ecr_r26.date_validation = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(3, \"div\", 32)(4, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function BanqueSuiviComponent_tr_43_ng_template_20_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ecr_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r38 = i0.ɵɵnextContext();\n      ecr_r26.new_etat = \"encaisse\";\n      return i0.ɵɵresetView(ctx_r38.majEtatEcritureBq(true, ctx_r38.mois, ctx_r38.filtre.sh, ecr_r26));\n    });\n    i0.ɵɵtext(5, \"Valider\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ecr_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ecr_r26.date_validation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ecr_r26.date_validation);\n  }\n}\nfunction BanqueSuiviComponent_tr_43_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 34);\n    i0.ɵɵlistener(\"nzOnConfirm\", function BanqueSuiviComponent_tr_43_a_22_Template_a_nzOnConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ecr_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext();\n      ecr_r26.data_etat.etat == \"draft\" || ecr_r26.data_etat.etat == \"inpaye\" ? ecr_r26.new_etat = \"sent\" : ecr_r26.data_etat.etat == \"sent\" ? ecr_r26.new_etat = \"encaisse\" : \"\";\n      return i0.ɵɵresetView(ctx_r41.majEtatEcritureBq(true, ctx_r41.mois, ctx_r41.filtre.sh, ecr_r26));\n    });\n    i0.ɵɵelement(1, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ecr_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ecr_r26.data_etat.etat == \"draft\" ? \"text-blue-500 hover:border-blue-500\" : \"text-red-500 hover:border-red-500\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r31.financialPrivilege())(\"nzTooltipTitle\", ecr_r26.data_etat.etat == \"draft\" && \"\\u00C9mettre \\u00E0 la banque\" || ecr_r26.data_etat.etat == \"inpaye\" && \"Renvoyer a la banque\" || \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"nzType\", ecr_r26.data_etat.etat == \"draft\" && \"bank\" || ecr_r26.data_etat.etat == \"inpaye\" && \"issues-close\" || \"\");\n  }\n}\nfunction BanqueSuiviComponent_tr_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 36);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"lbl\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 17)(13, \"a\", 18);\n    i0.ɵɵlistener(\"click\", function BanqueSuiviComponent_tr_43_Template_a_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const ecr_r26 = restoredCtx.$implicit;\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.loadEcriture(\"bq\", ecr_r26.id, \"form_banque\", {\n        journal: ctx_r45.journal,\n        mois: ctx_r45.mois,\n        isSuivis: true\n      }));\n    });\n    i0.ɵɵelement(14, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"a\", 20);\n    i0.ɵɵlistener(\"nzOnConfirm\", function BanqueSuiviComponent_tr_43_Template_a_nzOnConfirm_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const ecr_r26 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.adminPrivilege() && ctx_r47.majEspece(ctx_r47.Actions.Del, ecr_r26, ctx_r47.mois, true));\n    });\n    i0.ɵɵelement(16, \"span\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, BanqueSuiviComponent_tr_43_a_17_Template, 2, 1, \"a\", 22);\n    i0.ɵɵelementStart(18, \"div\");\n    i0.ɵɵtemplate(19, BanqueSuiviComponent_tr_43_a_19_Template, 2, 3, \"a\", 23);\n    i0.ɵɵtemplate(20, BanqueSuiviComponent_tr_43_ng_template_20_Template, 6, 2, \"ng-template\", null, 24, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, BanqueSuiviComponent_tr_43_a_22_Template, 2, 5, \"a\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ecr_r26 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 10, ecr_r26.date_echeance, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ecr_r26.data_tiers == null ? null : ecr_r26.data_tiers.nom, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(8, 13, ecr_r26.tpe_doc, \"commun.liste_operation_ca_bq\", \"code\", \"label\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ecr_r26.total_debit > 0 ? i0.ɵɵpipeBind2(11, 18, ecr_r26.total_debit, \".2-2\") : \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.financialPrivilege());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.adminPrivilege())(\"nzPopconfirmTitle\", ctx_r3.Messages.confirm_delete);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ecr_r26.data_etat.etat == \"sent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ecr_r26.data_etat.etat == \"sent\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ecr_r26.data_etat.etat == \"draft\" || ecr_r26.data_etat.etat == \"inpaye\");\n  }\n}\nexport class BanqueSuiviComponent extends TresorComposant {\n  get isValid() {\n    return this._isValid;\n  }\n  set isValid(value) {\n    this._isValid = value;\n    this.isValidChange.emit(value);\n  }\n  constructor(app, backendService) {\n    super(app, backendService);\n    this.app = app;\n    this.backendService = backendService;\n    this.isValidChange = new EventEmitter();\n    this._isValid = false;\n  }\n  ngOnInit() {\n    this.isValidChange.emit(this._isValid);\n  }\n  getListEcritureEntree() {\n    return this.getDataList('tresor.bq_suivis').filter(e => e.sens === 'e' && (this.isValid ? e.data_etat.etat == 'encaisse' : e.data_etat.etat != 'encaisse'));\n  }\n  getListEcritureSortie() {\n    return this.getDataList('tresor.bq_suivis').filter(e => e.sens === 's' && (this.isValid ? e.data_etat.etat == 'encaisse' : e.data_etat.etat != 'encaisse'));\n  }\n}\nBanqueSuiviComponent.ɵfac = function BanqueSuiviComponent_Factory(t) {\n  return new (t || BanqueSuiviComponent)(i0.ɵɵdirectiveInject(i1.AppService), i0.ɵɵdirectiveInject(i2.BackEndService));\n};\nBanqueSuiviComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: BanqueSuiviComponent,\n  selectors: [[\"banque-suivi\"]],\n  inputs: {\n    journal: \"journal\",\n    mois: \"mois\",\n    year: \"year\"\n  },\n  outputs: {\n    isValidChange: \"isValidChange\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 44,\n  vars: 10,\n  consts: [[1, \"flex\", \"justify-between\", \"gap-4\", \"pt-4\"], [\"label\", \"Recherche (Intitul\\u00E9 ou montant ou numero de cheque)\"], [\"nzAllowClear\", \"\", \"nz-input\", \"\", 1, \"w-[30rem]\", 3, \"ngModel\", \"ngModelChange\", \"keyup.enter\"], [1, \"\"], [\"label\", \"Op\\u00E9rations valid\\u00E9es\"], [3, \"ngModel\", \"nzDisabled\", \"nzControl\", \"ngModelChange\", \"click\"], [1, \"flex\", \"gap-2\"], [1, \"w-[50%]\"], [1, \"py-2\", \"font-bold\"], [\"nzSize\", \"small\", 1, \"h-[55vh]\", \"overflow-y-auto\", 3, \"nzFrontPagination\", \"nzData\"], [\"tableListeEntree\", \"\"], [\"nzWidth\", \"20%\"], [\"nzWidth\", \"10%\"], [4, \"ngFor\", \"ngForOf\"], [\"nzSize\", \"small\", 1, \"h-[55vh]\", \"overflow-y-auto\", 3, \"nzData\", \"nzFrontPagination\"], [\"tableListeSortie\", \"\"], [\"nzWidth\", \"15%\"], [1, \"flex\"], [\"nz-button\", \"\", \"nz-tooltip\", \"\", \"nzTooltipTitle\", \"Modifier\", 1, \"cursor-pointer\", \"flex\", \"justify-center\", \"items-center\", \"p-2\", \"uppercase\", \"hover:border-orange-500\", \"hover:text-orange-600\", 3, \"disabled\", \"click\"], [\"nz-icon\", \"\", \"nzType\", \"edit\", \"nzTheme\", \"outline\"], [\"nz-button\", \"\", \"nz-popconfirm\", \"\", \"nzPopconfirmPlacement\", \"bottom\", \"nz-tooltip\", \"\", \"nzTooltipTitle\", \"Supprimer\", 1, \"cursor-pointer\", \"flex\", \"justify-center\", \"items-center\", \"p-2\", \"uppercase\", \"hover:border-red-500\", \"hover:text-red-600\", 3, \"disabled\", \"nzPopconfirmTitle\", \"nzOnConfirm\"], [\"nz-icon\", \"\", \"nzType\", \"delete\", \"nzTheme\", \"outline\"], [\"nz-button\", \"\", \"nz-popconfirm\", \"\", \"nzPopconfirmTitle\", \"Voulez-vous changer l'\\u00E9tat de cette \\u00E9criture ?\", \"nzPopconfirmPlacement\", \"bottom\", \"class\", \"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\", \"nz-tooltip\", \"\", \"nzTooltipTitle\", \"Ch\\u00E8que impay\\u00E9\", 3, \"disabled\", \"nzOnConfirm\", 4, \"ngIf\"], [\"nz-button\", \"\", \"nz-popover\", \"\", \"nzPopoverPlacement\", \"bottom\", \"nzPopoverTrigger\", \"click\", \"nzPopoverPlacement\", \"bottom\", \"class\", \"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\", \"nz-tooltip\", \"\", \"nzTooltipTitle\", \"Valider l'\\u00E9criture?\", 3, \"disabled\", \"nzPopoverContent\", 4, \"ngIf\"], [\"formValidation\", \"\"], [\"class\", \"cursor-pointer flex justify-center items-center border p-2 duration-200\", \"nz-button\", \"\", \"nz-popconfirm\", \"\", \"nz-tooltip\", \"\", \"nzPopconfirmTitle\", \"Voulez-vous changer l'\\u00E9tat de cette \\u00E9criture ?\", \"nzPopconfirmPlacement\", \"bottom\", 3, \"disabled\", \"class\", \"nzTooltipTitle\", \"nzOnConfirm\", 4, \"ngIf\"], [\"nz-button\", \"\", \"nz-popconfirm\", \"\", \"nzPopconfirmTitle\", \"Voulez-vous changer l'\\u00E9tat de cette \\u00E9criture ?\", \"nzPopconfirmPlacement\", \"bottom\", \"nz-tooltip\", \"\", \"nzTooltipTitle\", \"Ch\\u00E8que impay\\u00E9\", 1, \"cursor-pointer\", \"flex\", \"justify-center\", \"items-center\", \"p-2\", \"uppercase\", \"hover:border-orange-500\", \"hover:text-orange-600\", 3, \"disabled\", \"nzOnConfirm\"], [\"nz-icon\", \"\", \"nzType\", \"rollback\", \"nzTheme\", \"outline\"], [\"nz-button\", \"\", \"nz-popover\", \"\", \"nzPopoverPlacement\", \"bottom\", \"nzPopoverTrigger\", \"click\", \"nzPopoverPlacement\", \"bottom\", \"nz-tooltip\", \"\", \"nzTooltipTitle\", \"Valider l'\\u00E9criture?\", 1, \"cursor-pointer\", \"flex\", \"justify-center\", \"items-center\", \"p-2\", \"uppercase\", \"hover:border-orange-500\", \"hover:text-orange-600\", 3, \"disabled\", \"nzPopoverContent\"], [\"nz-icon\", \"\", \"nzType\", \"check-circle\", \"nzTheme\", \"outline\"], [\"label\", \"Date de validation\"], [1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [1, \"pt-3\", \"text-right\"], [\"nz-button\", \"\", 3, \"disabled\", \"click\"], [\"nz-button\", \"\", \"nz-popconfirm\", \"\", \"nz-tooltip\", \"\", \"nzPopconfirmTitle\", \"Voulez-vous changer l'\\u00E9tat de cette \\u00E9criture ?\", \"nzPopconfirmPlacement\", \"bottom\", 1, \"cursor-pointer\", \"flex\", \"justify-center\", \"items-center\", \"border\", \"p-2\", \"duration-200\", 3, \"disabled\", \"nzTooltipTitle\", \"nzOnConfirm\"], [\"nz-icon\", \"\", \"nzTheme\", \"outline\", 3, \"nzType\"], [1, \"text-right\"]],\n  template: function BanqueSuiviComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelement(1, \"div\");\n      i0.ɵɵelementStart(2, \"form-item\", 1)(3, \"input\", 2);\n      i0.ɵɵlistener(\"ngModelChange\", function BanqueSuiviComponent_Template_input_ngModelChange_3_listener($event) {\n        return ctx.filtre.sh = $event;\n      })(\"keyup.enter\", function BanqueSuiviComponent_Template_input_keyup_enter_3_listener() {\n        ctx.setDataString(\"vbla.loading\", \"0\");\n        return ctx.traiteEcritureBanque(ctx.Actions.Get, true, ctx.journal, {\n          annee: ctx.year,\n          sh: ctx.filtre.sh\n        }, ctx.isValid);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(4, \"div\", 3)(5, \"form-item\", 4)(6, \"nz-switch\", 5);\n      i0.ɵɵlistener(\"ngModelChange\", function BanqueSuiviComponent_Template_nz_switch_ngModelChange_6_listener($event) {\n        return ctx.isValid = $event;\n      })(\"click\", function BanqueSuiviComponent_Template_nz_switch_click_6_listener() {\n        return ctx.isValid = !ctx.isValid;\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n      i0.ɵɵtext(10, \"Entr\\u00E9es en banque\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"nz-table\", 9, 10)(13, \"thead\")(14, \"tr\")(15, \"th\", 11);\n      i0.ɵɵtext(16, \"Dates\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"th\", 11);\n      i0.ɵɵtext(18, \"Tiers\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"th\");\n      i0.ɵɵtext(20, \"Op\\u00E9ration\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"th\", 12);\n      i0.ɵɵtext(22, \"Montant\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(23, \"th\", 11);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(24, \"tbody\");\n      i0.ɵɵtemplate(25, BanqueSuiviComponent_ng_container_25_Template, 23, 18, \"ng-container\", 13);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(26, \"div\", 7)(27, \"div\", 8);\n      i0.ɵɵtext(28, \"Sorties\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"nz-table\", 14, 15)(31, \"thead\")(32, \"tr\")(33, \"th\", 11);\n      i0.ɵɵtext(34, \"Date\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"th\", 11);\n      i0.ɵɵtext(36, \"Tiers\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"th\");\n      i0.ɵɵtext(38, \"Op\\u00E9ration\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(39, \"th\", 12);\n      i0.ɵɵtext(40, \"Montant\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(41, \"th\", 16);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(42, \"tbody\");\n      i0.ɵɵtemplate(43, BanqueSuiviComponent_tr_43_Template, 23, 21, \"tr\", 13);\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      const _r0 = i0.ɵɵreference(12);\n      const _r2 = i0.ɵɵreference(30);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngModel\", ctx.filtre.sh);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngModel\", ctx.isValid)(\"nzDisabled\", !ctx.mois)(\"nzControl\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"nzFrontPagination\", true)(\"nzData\", ctx.getListEcritureEntree());\n      i0.ɵɵadvance(14);\n      i0.ɵɵproperty(\"ngForOf\", _r0.data);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"nzData\", ctx.getListEcritureSortie())(\"nzFrontPagination\", true);\n      i0.ɵɵadvance(14);\n      i0.ɵɵproperty(\"ngForOf\", _r2.data);\n    }\n  },\n  dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.NzIconDirective, i6.NzInputDirective, i7.NzPopconfirmDirective, i8.NzDatePickerComponent, i9.NzSwitchComponent, i10.NzPopoverDirective, i11.NzTableComponent, i11.NzTableCellDirective, i11.NzThMeasureDirective, i11.NzTheadComponent, i11.NzTbodyComponent, i11.NzTrDirective, i12.NzButtonComponent, i13.ɵNzTransitionPatchDirective, i14.NzWaveDirective, i15.NzTooltipDirective, i16.FormItemComponent, i3.DecimalPipe, i3.DatePipe, i17.LabByIdPipe],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAG9E,SAASC,eAAe,QAAQ,iCAAiC;;;;;;;;;;;;;;;;;;;;;;ICoDrCC,6BAM8C;IAJ1CA;MAAAA;MAAA;MAAA;MAAAC,kBAA8B,QAAQ;MAAA,OAAED,yCAAkB,IAAI,0CAAsB;IAAA,EAAC;IAKrFA,2BAAyD;IAC7DA,iBAAI;;;;IAR8CA,uDAAkC;;;;;IAUhFA,6BAKoD;IAChDA,2BAA6D;IACjEA,iBAAI;;;;;;IAP8CA,uDAAkC;;;;;;IAShFA,+BAAkB;IAGNA;MAAAA;MAAA;MAAA,OAAaA,+CAA2B;IAAA,EAAP;IAACA,iBAAiB;IAG/DA,+BAA6B;IAErBA;MAAAA;MAAA;MAAA;MAAAC,kBAAwB,UAAU;MAAA,OAAED,yCAAkB,IAAI,0CAAsB;IAAA,EAAC;IAACA,uBAAO;IAAAA,iBAAS;;;;IAL9FA,eAAiC;IAAjCA,gDAAiC;IAIjCA,eAAiC;IAAjCA,kDAAiC;;;;;;IAKrDA,6BAcmC;IAR/BA;MAAAA;MAAA;MAAA;MAAAC,yBACgE,OAC/F,6BAA0B,QAAQ,qBACN,MAAM,4BACL,MAAM,qBACX,UAAU,GACF,EAAE;MAAA,OAAsCD,yCAC/D,IAAI,0CAAsB;IAAA,EAAH;IAEDA,2BAE6B;IACjCA,iBAAI;;;;;IAfAA,6HAAqH;IAFrHA,uDAAkC;IAe9BA,eAAgH;IAAhHA,gIAAgH;;;;;;IA3EpIA,6BAAwD;IACpDA,0BAAI;IAEIA,YACJ;IAAAA,iBAAK;IACLA,0BAAI;IACAA,YACJ;IAAAA,iBAAK;IACLA,0BAAI;IACAA,YACJ;;IAAAA,iBAAK;IACLA,0BAAI;IACAA,aACJ;;IAAAA,iBAAK;IACLA,+BAAiB;IAETA;MAAA;MAAA;MAAA;MAAA,OAASA,oCAAa,IAAI,aAAS,aAAa;QAAAE;QAAAC;QAAAC,UAAwB;MAAI,EAAE;IAAA,EAAC;IAG/EJ,4BAAqD;IACzDA,iBAAI;IACJA,8BAI0C;IAHtCA;MAAA;MAAA;MAAA;MAAA,OAAeA,uCAAgB,IAAIK,6DAAkC,IAAI,CAAC;IAAA,EAAC;IAI3EL,4BAAuD;IAC3DA,iBAAI;IACJA,oFAQI;IACJA,4BAAK;IACDA,oFAOI;IACJA,yIAWc;IAClBA,iBAAM;IACNA,oFAkBI;IACRA,iBAAK;IAEbA,0BAAe;;;;;IA7EHA,eACJ;IADIA,qDACJ;IAEIA,eACJ;IADIA,yFACJ;IAEIA,eACJ;IADIA,uHACJ;IAEIA,eACJ;IADIA,mHACJ;IAEiBA,eAAkC;IAAlCA,uDAAkC;IAMlCA,eAA8B;IAA9BA,mDAA8B;IAOvCA,eAAkC;IAAlCA,sDAAkC;IAUpBA,eAAkC;IAAlCA,sDAAkC;IAqBhDA,eAAqE;IAArEA,4FAAqE;;;;;;IAkE7EA,6BAM8C;IAJ1CA;MAAAA;MAAA;MAAA;MAAAM,mBAA8B,QAAQ;MAAA,OAAEN,yCAAkB,IAAI,2CAAsB;IAAA,EAAC;IAKrFA,2BAAyD;IAC7DA,iBAAI;;;;IAR8CA,wDAAkC;;;;;IAUhFA,6BAIoD;IAChDA,2BAA6D;IACjEA,iBAAI;;;;;;IAN8CA,wDAAkC;;;;;;IAQhFA,+BAAkB;IAGNA;MAAAA;MAAA;MAAA,OAAaA,gDAA2B;IAAA,EAAP;IAACA,iBAAiB;IAG/DA,+BAA6B;IAErBA;MAAAA;MAAA;MAAA;MAAAM,mBAAwB,UAAU;MAAA,OAAEN,yCAAkB,IAAI,2CAAsB;IAAA,EAAC;IAACA,uBAAO;IAAAA,iBAAS;;;;IAL9FA,eAAiC;IAAjCA,iDAAiC;IAIvBA,eAAiC;IAAjCA,mDAAiC;;;;;;IAK/DA,6BAcmC;IAR/BA;MAAAA;MAAA;MAAA;MAAAM,0BAC4D,OAAO,8BACxE,QAAQ,sBACN,MAAM,6BACL,MAAM,sBACX,UAAU,GACF,EAAE;MAAA,OAAkCN,yCAC3D,IAAI,2CAAsB;IAAA,EAAH;IAEDA,2BAE6B;IACjCA,iBAAI;;;;;IAfAA,8HAAqH;IAFrHA,wDAAkC;IAe9BA,eAAgH;IAAhHA,kIAAgH;;;;;;IAzEhIA,0BAA8C;IAEtCA,YACJ;;IAAAA,iBAAK;IACLA,8BAAuB;IACnBA,YACJ;IAAAA,iBAAK;IACLA,8BAAuB;IACnBA,YACJ;;IAAAA,iBAAK;IACLA,0BAAI;IACAA,aACJ;;IAAAA,iBAAK;IACLA,+BAAiB;IAETA;MAAA;MAAA;MAAA;MAAA,OAASA,oCAAa,IAAI,cAAS,aAAa;QAAAE;QAAAC;QAAAC,UAAwB;MAAI,EAAE;IAAA,EAAC;IAG/EJ,4BAAqD;IACzDA,iBAAI;IACJA,8BAI0C;IAHtCA;MAAA;MAAA;MAAA;MAAA,OAAeA,uCAAgB,IAAIO,8DAAkC,IAAI,CAAC;IAAA,EAAC;IAI3EP,4BAAuD;IAC3DA,iBAAI;IACJA,0EAQI;IACJA,4BAAK;IACDA,0EAMI;IACJA,+HAWc;IAClBA,iBAAM;IACNA,0EAkBI;IACRA,iBAAK;;;;;IA1EDA,eACJ;IADIA,2FACJ;IAEIA,eACJ;IADIA,2FACJ;IAEIA,eACJ;IADIA,wHACJ;IAEIA,eACJ;IADIA,mHACJ;IAEiBA,eAAkC;IAAlCA,uDAAkC;IAMlCA,eAA8B;IAA9BA,mDAA8B;IAOvCA,eAAkC;IAAlCA,uDAAkC;IAUpBA,eAAkC;IAAlCA,uDAAkC;IAoBhDA,eAAqE;IAArEA,8FAAqE;;;AD5KjG,OAAM,MAAOQ,oBAAqB,SAAQT,eAAe;EAOvD,IAAIU,OAAO;IACT,OAAO,IAAI,CAACC,QAAQ;EACtB;EACA,IAAID,OAAO,CAACE,KAAc;IACxB,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,CAACC,aAAa,CAACC,IAAI,CAACF,KAAK,CAAC;EAChC;EACAG,YACqBC,GAAe,EACfC,cAA8B;IAEjD,KAAK,CAACD,GAAG,EAAEC,cAAc,CAAC;IAHP,QAAG,GAAHD,GAAG;IACH,mBAAc,GAAdC,cAAc;IAZzB,kBAAa,GAAG,IAAIlB,YAAY,EAAW;IAC7C,aAAQ,GAAG,KAAK;EAcxB;EAEAmB,QAAQ;IACN,IAAI,CAACL,aAAa,CAACC,IAAI,CAAC,IAAI,CAACH,QAAQ,CAAC;EACxC;EAEAQ,qBAAqB;IACnB,OAAO,IAAI,CAACC,WAAW,CAAC,kBAAkB,CAAC,CAACC,MAAM,CAAEC,CAAK,IAAGA,CAAC,CAACC,IAAI,KAAK,GAAG,KAAK,IAAI,CAACb,OAAO,GAACY,CAAC,CAACE,SAAS,CAACC,IAAI,IAAI,UAAU,GAACH,CAAC,CAACE,SAAS,CAACC,IAAI,IAAI,UAAU,CAAC,CAAC;EAC7J;EAEAC,qBAAqB;IACnB,OAAO,IAAI,CAACN,WAAW,CAAC,kBAAkB,CAAC,CAACC,MAAM,CAAEC,CAAK,IAAGA,CAAC,CAACC,IAAI,KAAK,GAAG,KAAK,IAAI,CAACb,OAAO,GAACY,CAAC,CAACE,SAAS,CAACC,IAAI,IAAI,UAAU,GAACH,CAAC,CAACE,SAAS,CAACC,IAAI,IAAI,UAAU,CAAC,CAAC;EAC7J;;AA/BWhB,oBAAqB;mBAArBA,oBAAoB;AAAA;AAApBA,oBAAqB;QAArBA,oBAAoB;EAAAkB;EAAAC;IAAAzB;IAAAC;IAAAyB;EAAA;EAAAC;IAAAjB;EAAA;EAAAkB;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCVjClC,8BAA6C;MACzCA,sBAAW;MACXA,oCAAuE;MACtCA;QAAA;MAAA,EAAuB;QAAkCmC,kBAAmB,cAAc,EAAE,GAAG,CAAC;QAAA,OAACA,0CAAiC,IAAI;UAAAC;UAAAC;QAAA,eAA6C;MAAA,EAA5J;MAApDrC,iBAAoN;MAExNA,8BAAc;MAEKA;QAAA;MAAA,EAAqB;QAAA;MAAA;MACDA,iBAAY;MAIvDA,8BAAwB;MAEYA,uCAAiB;MAAAA,iBAAM;MACnDA,wCACuC;MAGTA,sBAAK;MAAAA,iBAAK;MAC5BA,+BAAkB;MAAAA,sBAAK;MAAAA,iBAAK;MAC5BA,2BAAI;MAAAA,+BAAS;MAAAA,iBAAK;MAClBA,+BAAkB;MAAAA,wBAAO;MAAAA,iBAAK;MAC9BA,0BAAuB;MAC3BA,iBAAK;MAETA,8BAAO;MACHA,4FAgFe;MACnBA,iBAAQ;MAGhBA,+BAAqB;MACWA,wBAAO;MAAAA,iBAAM;MACzCA,yCAC+B;MAGDA,qBAAI;MAAAA,iBAAK;MAC3BA,+BAAkB;MAAAA,sBAAK;MAAAA,iBAAK;MAC5BA,2BAAI;MAAAA,+BAAS;MAAAA,iBAAK;MAClBA,+BAAkB;MAAAA,wBAAO;MAAAA,iBAAK;MAC9BA,0BAAuB;MAC3BA,iBAAK;MAETA,8BAAO;MACHA,wEA6EK;MACTA,iBAAQ;;;;;MAxMiBA,eAAuB;MAAvBA,uCAAuB;MAIrCA,eAAqB;MAArBA,qCAAqB;MAQXA,eAA0B;MAA1BA,wCAA0B;MAYbA,gBAAwB;MAAxBA,kCAAwB;MAsFcA,eAAkC;MAAlCA,oDAAkC;MAYlFA,gBAAwB;MAAxBA,kCAAwB", "names": ["EventEmitter", "TresorComposant", "i0", "ecr_r4", "journal", "mois", "<PERSON><PERSON><PERSON><PERSON>", "ctx_r25", "ecr_r26", "ctx_r47", "BanqueSuiviComponent", "<PERSON><PERSON><PERSON><PERSON>", "_isValid", "value", "isValidChange", "emit", "constructor", "app", "backendService", "ngOnInit", "getListEcritureEntree", "getDataList", "filter", "e", "sens", "data_etat", "etat", "getListEcritureSortie", "selectors", "inputs", "year", "outputs", "features", "decls", "vars", "consts", "template", "ctx", "annee", "sh"], "sourceRoot": "", "sources": ["G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\compta\\modules\\tresor\\composants\\banque-suivi\\banque-suivi.component.ts", "G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\compta\\modules\\tresor\\composants\\banque-suivi\\banque-suivi.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { AppService } from 'src/app/core/services/app.service';\nimport { BackEndService } from 'src/app/store/services/back-end.service';\nimport { TresorComposant } from '../../features/tresor.composant';\n\n@Component({\n  selector: 'banque-suivi',\n  templateUrl: './banque-suivi.component.html',\n  styleUrls: ['./banque-suivi.component.scss']\n})\nexport class BanqueSuiviComponent extends TresorComposant implements OnInit {\n  @Input('journal') journal:any\n  @Input('mois') mois:any\n  @Input('year') year:any\n  @Output() isValidChange = new EventEmitter<boolean>();\n  private _isValid = false;\n\n  get isValid(): boolean {\n    return this._isValid;\n  }\n  set isValid(value: boolean) {\n    this._isValid = value;\n    this.isValidChange.emit(value);\n  }\n  constructor(\n    protected override app: AppService,\n    protected override backendService: BackEndService\n  ) {\n    super(app, backendService);\n  }\n  \n  ngOnInit(): void {\n    this.isValidChange.emit(this._isValid);\n  }\n  \n  getListEcritureEntree(){\n    return this.getDataList('tresor.bq_suivis').filter((e:any)=>e.sens === 'e' && (this.isValid?e.data_etat.etat == 'encaisse':e.data_etat.etat != 'encaisse'))\n  }\n\n  getListEcritureSortie(){\n    return this.getDataList('tresor.bq_suivis').filter((e:any)=>e.sens === 's' && (this.isValid?e.data_etat.etat == 'encaisse':e.data_etat.etat != 'encaisse'))\n  }\n\n}\n", "<div class=\"flex justify-between gap-4 pt-4\">\n    <div></div>\n    <form-item label=\"Recherche (Intitulé ou montant ou numero de cheque)\">\n        <input nzAllowClear nz-input [(ngModel)]=\"filtre.sh\" class=\"w-[30rem]\" (keyup.enter)=\"this.setDataString('vbla.loading', '0');traiteEcritureBanque(Actions.Get,true,journal,{ annee:year, sh:filtre.sh},isValid)\" />\n    </form-item>\n    <div class=\"\">\n        <form-item label=\"Opérations validées\">\n            <nz-switch [(ngModel)]=\"isValid\" [nzDisabled]=\"!mois\" [nzControl]=\"true\"\n                (click)=\"isValid=!isValid\"></nz-switch>\n        </form-item>\n    </div>\n</div>\n<div class=\"flex gap-2\">\n    <div class=\"w-[50%]\">\n        <div class=\"py-2 font-bold\">Entrées en banque</div>\n        <nz-table nzSize=\"small\" [nzFrontPagination]=\"true\" class=\"h-[55vh] overflow-y-auto\" #tableListeEntree\n            [nzData]=\"getListEcritureEntree()\">\n            <thead>\n                <tr>\n                    <th nzWidth=\"20%\">Dates</th>\n                    <th nzWidth=\"20%\">Tiers</th>\n                    <th>Opération</th>\n                    <th nzWidth=\"10%\">Montant</th>\n                    <th nzWidth=\"20%\"></th>\n                </tr>\n            </thead>\n            <tbody>\n                <ng-container *ngFor=\"let ecr of tableListeEntree.data\">\n                    <tr>\n                        <td>\n                            {{ ecr.date_echeance }}\n                        </td>\n                        <td>\n                            {{ ecr.data_tiers?.nom }}\n                        </td>\n                        <td>\n                            {{ ecr.tpe_doc | lbl : 'commun.liste_operation_ca_bq' : 'code' : 'label' }}\n                        </td>\n                        <td>\n                            {{ ecr.total_credit > 0 ? (ecr.total_credit | number : '.2-2') : '' }}\n                        </td>\n                        <td class=\"flex\">\n                            <a nz-button [disabled]=\"!financialPrivilege()\"\n                                (click)=\"loadEcriture('bq',ecr.id, 'form_banque',{journal,mois,isSuivis:true})\"\n                                class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\"\n                                nz-tooltip nzTooltipTitle=\"Modifier\">\n                                <span nz-icon nzType=\"edit\" nzTheme=\"outline\"></span>\n                            </a>\n                            <a nz-button [disabled]=\"!adminPrivilege()\" nz-popconfirm\n                                (nzOnConfirm)=\"adminPrivilege() && majEspece(Actions.Del, ecr, mois, true)\"\n                                [nzPopconfirmTitle]=\"Messages.confirm_delete\" nzPopconfirmPlacement=\"bottom\"\n                                class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-red-500 hover:text-red-600\"\n                                nz-tooltip nzTooltipTitle=\"Supprimer\">\n                                <span nz-icon nzType=\"delete\" nzTheme=\"outline\"></span>\n                            </a>\n                            <a *ngIf=\"ecr.data_etat.etat == 'sent'\" nz-button [disabled]=\"!financialPrivilege()\"\n                                nz-popconfirm\n                                (nzOnConfirm)=\"ecr.new_etat = 'inpaye'; majEtatEcritureBq(true, mois,filtre.sh, ecr)\"\n                                nzPopconfirmTitle=\"Voulez-vous changer l'état de cette écriture ?\"\n                                nzPopconfirmPlacement=\"bottom\"\n                                class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\"\n                                nz-tooltip nzTooltipTitle=\"Chèque impayé\">\n                                <span nz-icon nzType=\"rollback\" nzTheme=\"outline\"></span>\n                            </a>\n                            <div>\n                                <a nz-button *ngIf=\"ecr.data_etat.etat == 'sent'\" [disabled]=\"!financialPrivilege()\"\n                                    nz-popover nzPopoverPlacement=\"bottom\" [nzPopoverContent]=\"formValidation\"\n                                    nzPopoverTrigger=\"click\" nzPopoverPlacement=\"bottom\"\n                                    [nzPopoverContent]=\"formValidation\"\n                                    class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\"\n                                    nz-tooltip nzTooltipTitle=\"Valider l'écriture?\">\n                                    <span nz-icon nzType=\"check-circle\" nzTheme=\"outline\"></span>\n                                </a>\n                                <ng-template #formValidation>\n                                    <div class=\"flex\">\n                                        <form-item label=\"Date de validation\">\n                                            <nz-date-picker class=\"w-full\"\n                                                [(ngModel)]=\"ecr.date_validation\"></nz-date-picker>\n                                        </form-item>\n                                    </div>\n                                    <div class=\"pt-3 text-right\">\n                                        <button [disabled]=\"!ecr.date_validation\" nz-button\n                                            (click)=\"ecr.new_etat = 'encaisse'; majEtatEcritureBq(true, mois,filtre.sh, ecr)\">Valider</button>\n                                    </div>\n                                </ng-template>\n                            </div>\n                            <a *ngIf=\"ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye'\"\n                                [disabled]=\"!financialPrivilege()\"\n                                class=\"cursor-pointer flex justify-center items-center border p-2 duration-200\"\n                                [class]=\"ecr.data_etat.etat == 'draft' ? 'text-blue-500 hover:border-blue-500' : 'text-red-500 hover:border-red-500'\"\n                                nz-button nz-popconfirm nz-tooltip\n                                [nzTooltipTitle]=\"(ecr.data_etat.etat == 'draft' && 'Émettre à la banque') || (ecr.data_etat.etat == 'inpaye' && 'Renvoyer a la banque') || ''\"\n                                (nzOnConfirm)=\"\n                                    (ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye')\n                                        ? (ecr.new_etat = 'sent')\n                                        : ecr.data_etat.etat == 'sent'\n                                        ? (ecr.new_etat = 'encaisse')\n                                        : '';\n                                    majEtatEcritureBq(true, mois,filtre.sh, ecr)\n                                \" nzPopconfirmTitle=\"Voulez-vous changer l'état de cette écriture ?\"\n                                nzPopconfirmPlacement=\"bottom\">\n                                <span nz-icon\n                                    [nzType]=\"(ecr.data_etat.etat == 'draft' && 'bank') || (ecr.data_etat.etat == 'inpaye' && 'issues-close') || ''\"\n                                    nzTheme=\"outline\"></span>\n                            </a>\n                        </td>\n                    </tr>\n                </ng-container>\n            </tbody>\n        </nz-table>\n    </div>\n    <div class=\"w-[50%]\">\n        <div class=\"py-2 font-bold\">Sorties</div>\n        <nz-table nzSize=\"small\" class=\"h-[55vh] overflow-y-auto\" #tableListeSortie [nzData]=\"getListEcritureSortie()\"\n            [nzFrontPagination]=\"true\">\n            <thead>\n                <tr>\n                    <th nzWidth=\"20%\">Date</th>\n                    <th nzWidth=\"20%\">Tiers</th>\n                    <th>Opération</th>\n                    <th nzWidth=\"10%\">Montant</th>\n                    <th nzWidth=\"15%\"></th>\n                </tr>\n            </thead>\n            <tbody>\n                <tr *ngFor=\"let ecr of tableListeSortie.data\">\n                    <td>\n                        {{ ecr.date_echeance | date: 'dd/MM/yyyy' }}\n                    </td>\n                    <td class=\"text-right\">\n                        {{ ecr.data_tiers?.nom }}\n                    </td>\n                    <td class=\"text-right\">\n                        {{ ecr.tpe_doc | lbl : 'commun.liste_operation_ca_bq' : 'code' : 'label' }}\n                    </td>\n                    <td>\n                        {{ ecr.total_debit > 0 ? (ecr.total_debit | number : '.2-2') : '' }}\n                    </td>\n                    <td class=\"flex\">\n                        <a nz-button [disabled]=\"!financialPrivilege()\"\n                            (click)=\"loadEcriture('bq',ecr.id, 'form_banque',{journal,mois,isSuivis:true})\"\n                            class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\"\n                            nz-tooltip nzTooltipTitle=\"Modifier\">\n                            <span nz-icon nzType=\"edit\" nzTheme=\"outline\"></span>\n                        </a>\n                        <a nz-button [disabled]=\"!adminPrivilege()\" nz-popconfirm\n                            (nzOnConfirm)=\"adminPrivilege() && majEspece(Actions.Del, ecr, mois, true)\"\n                            [nzPopconfirmTitle]=\"Messages.confirm_delete\" nzPopconfirmPlacement=\"bottom\"\n                            class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-red-500 hover:text-red-600\"\n                            nz-tooltip nzTooltipTitle=\"Supprimer\">\n                            <span nz-icon nzType=\"delete\" nzTheme=\"outline\"></span>\n                        </a>\n                        <a *ngIf=\"ecr.data_etat.etat == 'sent'\" nz-button [disabled]=\"!financialPrivilege()\"\n                            nz-popconfirm\n                            (nzOnConfirm)=\"ecr.new_etat = 'inpaye'; majEtatEcritureBq(true, mois,filtre.sh, ecr)\"\n                            nzPopconfirmTitle=\"Voulez-vous changer l'état de cette écriture ?\"\n                            nzPopconfirmPlacement=\"bottom\"\n                            class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\"\n                            nz-tooltip nzTooltipTitle=\"Chèque impayé\">\n                            <span nz-icon nzType=\"rollback\" nzTheme=\"outline\"></span>\n                        </a>\n                        <div>\n                            <a nz-button *ngIf=\"ecr.data_etat.etat == 'sent'\" [disabled]=\"!financialPrivilege()\"\n                                nz-popover nzPopoverPlacement=\"bottom\" [nzPopoverContent]=\"formValidation\"\n                                nzPopoverTrigger=\"click\" nzPopoverPlacement=\"bottom\" [nzPopoverContent]=\"formValidation\"\n                                class=\"cursor-pointer flex justify-center items-center p-2 uppercase hover:border-orange-500 hover:text-orange-600\"\n                                nz-tooltip nzTooltipTitle=\"Valider l'écriture?\">\n                                <span nz-icon nzType=\"check-circle\" nzTheme=\"outline\"></span>\n                            </a>\n                            <ng-template #formValidation>\n                                <div class=\"flex\">\n                                    <form-item label=\"Date de validation\">\n                                        <nz-date-picker class=\"w-full\"\n                                            [(ngModel)]=\"ecr.date_validation\"></nz-date-picker>\n                                    </form-item>\n                                </div>\n                                <div class=\"pt-3 text-right\">\n                                    <button nz-button [disabled]=\"!ecr.date_validation\"\n                                        (click)=\"ecr.new_etat = 'encaisse'; majEtatEcritureBq(true, mois,filtre.sh, ecr)\">Valider</button>\n                                </div>\n                            </ng-template>\n                        </div>\n                        <a *ngIf=\"ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye'\"\n                            [disabled]=\"!financialPrivilege()\"\n                            class=\"cursor-pointer flex justify-center items-center border p-2 duration-200\"\n                            [class]=\"ecr.data_etat.etat == 'draft' ? 'text-blue-500 hover:border-blue-500' : 'text-red-500 hover:border-red-500'\"\n                            nz-button nz-popconfirm nz-tooltip\n                            [nzTooltipTitle]=\"(ecr.data_etat.etat == 'draft' && 'Émettre à la banque') || (ecr.data_etat.etat == 'inpaye' && 'Renvoyer a la banque') || ''\"\n                            (nzOnConfirm)=\"\n                                (ecr.data_etat.etat == 'draft' || ecr.data_etat.etat == 'inpaye')\n                                    ? (ecr.new_etat = 'sent')\n                                    : ecr.data_etat.etat == 'sent'\n                                    ? (ecr.new_etat = 'encaisse')\n                                    : '';\n                                majEtatEcritureBq(true, mois,filtre.sh, ecr)\n                            \" nzPopconfirmTitle=\"Voulez-vous changer l'état de cette écriture ?\"\n                            nzPopconfirmPlacement=\"bottom\">\n                            <span nz-icon\n                                [nzType]=\"(ecr.data_etat.etat == 'draft' && 'bank') || (ecr.data_etat.etat == 'inpaye' && 'issues-close') || ''\"\n                                nzTheme=\"outline\"></span>\n                        </a>\n                    </td>\n                </tr>\n            </tbody>\n        </nz-table>\n    </div>\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}