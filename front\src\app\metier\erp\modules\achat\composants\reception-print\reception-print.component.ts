import { Component, OnInit } from '@angular/core';
import { filter } from 'rxjs';
import { AppService } from 'src/app/core/services/app.service';
import { Composant } from 'src/app/core/types/composant.base';

@Component({
  selector: 'reception-print',
  templateUrl: './reception-print.component.html',
  styleUrls: ['./reception-print.component.scss'],
})
export class ReceptionPrintComponent extends Composant implements OnInit {
  code = 'page_rc';
  doc: any = {};
  pages: any = {};
  nbPages: number = 1;
  details: any[] = [];
  cls: any = {};
  withPrice: boolean = false

  constructor(protected override app: AppService) {
    super(app);
  }

  ngOnInit(): void {
    this.app
      .getPrintStream()
      // .asObservable()
      .pipe(
        filter((printData) => {
          return printData?.code == this.code;
        })
      )
      .subscribe((printData) => {
        if (!printData.consumed) {
          this.withPrice = printData?.checked
          this.doc = printData.data?.doc;
          this.pages = this.gerenatePages(printData.data?.details);
          this.details = printData.data?.details
          this.nbPages = Object.keys(this.pages).length;
          if (this.doc.data_tiers) {
            this.cls = this.getDataList(`tiers.liste_frs`).find(
              (ob: any) => ob.code_tiers == this.doc.data_tiers.code
            );
          }
          setTimeout(() => {
            if (printData.consumed) return
            this.appercu(this.code);
            printData.consumed = true;
          }, 300);
        }
      });
  }

  gerenatePages(det: any[]) {
    let result: any[] = [];
    const listeFamilles = [
      ...new Set(
        det.map((det) => {
          const fam = (<string>det.data_produit.famille)?.split('.');
          return fam[1] || '';
        })
      ),
    ];
    listeFamilles.forEach((fam: string) => {
      const lgFam: any = { fam, tpe: 'fam' };
      const famille = this.getDataList('product.liste_familles').find(
        (f) => f.code == fam
      );
      if (famille) {
        lgFam.libelle = famille.libelle;
      }
      // result.push(lgFam);
      result = result.concat(
        det.filter((lg) =>
          (<string>lg.data_produit.famille).endsWith(`.${fam}`)
        )
      );
    });
    //18
    const nbLigneParPage = 13;
    const pages: any = {};
    // const nbPage = Math.ceil(result.length / nbLigneParPage);
    let pageEnCours = 1;
    let nbLigne = 0;
    result.forEach((lg, ndx) => {
      if (nbLigne > nbLigneParPage) {
        pageEnCours += 1;
        nbLigne = 0;
      }
      if (!pages[pageEnCours]) {
        pages[pageEnCours] = { lignes: [] };
      }

      (<any[]>pages[pageEnCours].lignes).push(lg);
      nbLigne += 1;
    });
    return pages;
  }

  calculateTotal(): any {
    let ttc = 0;
    let ht = 0;
    let tva: any = {}
    this.getDataList("commun.liste_tva").forEach((tv) => {
      tva[tv.val] = 0
    })
    for (let lg of this.details) {
      ht += lg.data_pricing.prix_ht * lg.data_qte.abr;
      ttc += (lg.data_pricing.prix_ht * lg.data_qte.abr) * (lg.data_pricing.tva);
      tva[(lg.data_pricing.tva)] += ((lg.data_pricing.prix_ht * lg.data_qte.abr) * (lg.data_pricing.tva)) - (lg.data_pricing.prix_ht * lg.data_qte.vcc)
    }
    this.getDataList("commun.liste_tva").forEach((tv) => {
      if (tva[tv.val] == 0) {
        delete tva[tv.val]
      }
    })
    return { ttc, ht, tva };

  }

  get_num_recep(doc: any) {
    if (doc && doc.is_avoir && doc.numero_interne) {
      return doc.numero_interne.split('F')[1]
    }
    return doc.numero_interne
  }

  getFraisRubrique(frais: any) {
    if (this.doc.data_frais.lst) {
      return (<any[]>this.doc.data_frais.lst).filter(
        (ob: any) => ob.code == frais.code
      );
    }
    return [];
  }
}
