<div class="panel min-h-[80vh]">
  <div class="flex gap-2 justify-between w-full">
    <div class="flex gap-2 w-full">
      <form-item label="Fournisseur" class="w-[20%] align-middle">
        <nz-select nzAllowClear class="w-full" nzShowSearch [(ngModel)]="filtre.frs"
          (ngModelChange)="selectFournisseur()">
          <nz-option *ngFor="let frs of getDataList('tiers.liste_frs')" [nzLabel]="frs.nom_tiers"
            [nzValue]="'frs_'+frs.id"></nz-option>
        </nz-select>
      </form-item>
      <div class="w-auto self-end">
        <a nz-button nzType="primary" nz-popover nzPopoverPlacement="bottom" [disabled]="!filtre.frs"
          [nzPopoverContent]="formPrint" nzPopoverTrigger="click" class="">
          <span>Imprimer</span>
        </a>
        <ng-template #formPrint>
          <div class="w-[20vw] flex gap-2">
            <form-item label="Date Début">
              <nz-date-picker class="w-full" [(ngModel)]="filtre.date_debut"></nz-date-picker>
            </form-item>
            <form-item label="Date Fin">
              <nz-date-picker class="w-full" [(ngModel)]="filtre.date_fin"></nz-date-picker>
            </form-item>
          </div>
          <label class="pt-3" nz-checkbox [(ngModel)]="checked">Avec réglement</label>
          <div class="text-right pt-3">
            <button nz-button (click)="imprimer()">Imprimer</button>
          </div>
        </ng-template>
      </div>
      <div class="pt-2 self-end">
        <a nz-button nzType="primary" [disabled]="!filtre.frs" nz-popover nzPopoverPlacement="top" style="width: 100%;"
          [nzPopoverContent]="formPrintlet" nzPopoverTrigger="click" class="">
          <span>Impression detaillé</span>
        </a>
        <ng-template #formPrintlet>
          <div class="w-[20vw] flex gap-2">
            <form-item label="Date Début">
              <nz-date-picker class="w-full" [(ngModel)]="filtre.date_debut"></nz-date-picker>
            </form-item>
            <form-item label="Date Fin">
              <nz-date-picker class="w-full" [(ngModel)]="filtre.date_fin"></nz-date-picker>
            </form-item>
          </div>
          <div class="w-[15vw]">
            <form-item label="Pole">
              <nz-select class="w-full" nzAllowClear nzShowSearch [(ngModel)]="filtre.pole">
                <nz-option *ngFor="let tpe of liste_pole" [nzLabel]="tpe.label" [nzValue]="tpe.code"></nz-option>
              </nz-select>
            </form-item>
          </div>
          <div class="text-right pt-3">
            <button nz-button (click)="imprimerDet('page_compte_frs_det')">Imprimer</button>
          </div>
        </ng-template>
      </div>
    </div>
    <div>
      <a [disabled]="!achatCommercialPrivilege() || !filtre.frs" nz-button nzType="primary"
        class="uppercase float-right" (click)="
          openForm({
            cle: 'form_paiement',
            _ttl: 'Nouveau Paiement',
            _paiement: {
              tpe_doc: 'regl_frs',
              sens: 's',
              data_tiers:{
                nom: getFrs(filtre.frs).nom_tiers,
                tpe_tiers: filtre.frs.split('_')[0]
              },
              tiers: getFrs(filtre.frs).id
            },
            _qp: { tiers: filtre.frs }
          })
        ">
        <div class="flex items-center gap-2">
          <span nz-icon nzType="plus" nzTheme="outline"></span>
          <span>Créer Paiement</span>
        </div>
      </a>
    </div>
  </div>

  <div>
    <nz-tabset>
      <nz-tab class="uppercase" [nzTitle]="ttlProduit">
        <ng-template #ttlProduit>
          <div class="font-bold uppercase">Compte Fournisseur</div>
        </ng-template>
        <div class="basis-[80%]">
          <div class="basis-[100%]">
            <ng-container>
              <div class="flex gap-2">
                <div class="w-[50%]">
                  <div class="h-[60vh]">
                    <nz-table nzSize="small" *ngIf="getDataList('achat.liste_abr') as listeDocs" #tableDocs
                      [nzLoading]="getDataString('abr.loading') == '1'" [nzData]="listeDocs">
                      <thead>
                        <tr>
                          <th>N°</th>
                          <th>Type</th>
                          <th>Date</th>
                          <th>Montant TTC</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let obj of tableDocs.data" [ngClass]="{'bg-red-50': obj?.is_avoir}">
                          <td>
                            <div>{{ obj.numero_interne }}</div>
                            <span class="text-sm text-yellow-600">{{obj.numero_externe}}</span>
                          </td>
                          <td>
                            {{ obj.tpe_doc }}
                          </td>
                          <td>
                            <span>{{ obj.date_doc | date : "dd-MM-yyyy" }}</span>
                          </td>
                          <td>{{ obj.is_avoir? -obj.data_valeurs.mt_ttc: obj.data_valeurs.mt_ttc || 0 | number : ".2-2"
                            : "fr" }}</td>
                        </tr>
                      </tbody>
                    </nz-table>
                  </div>
                </div>
                <div class="w-[50%]">
                  <nz-table nzSize="small" #tablelisteEcritures [nzData]="
                      getDataList('compta.liste_ecritures')
                    " [nzLoading]="getDataString('abr.loading') == '1'">
                    <thead>
                      <tr>
                        <!-- <th nzWidth="2vw"></th> -->
                        <th>Pièces</th>
                        <!-- <th>Type</th> -->
                        <th>Journal</th>
                        <th>Montant</th>
                        <th>Debit</th>
                        <th>Credit</th>
                        <th>Etat</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let obj of tablelisteEcritures.data"
                        [class]="obj?.data_etat?.etat=='inpaye'?'text-red-700':obj?.data_etat?.etat=='encaisse'?'text-green-600':''">
                        <!-- <td><label nz-checkbox [(ngModel)]="obj._chk"></label></td> -->
                        <td>
                          <div class="uppercase">
                            {{ obj.intitule || obj.mode_paiement }}
                          </div>
                          <div class="font-medium text-sm text-gray-400">
                            {{ (obj.date_echeance? obj.date_echeance: obj.date_doc) | date : "dd-MM-yyyy" }}
                          </div>
                        </td>
                        <!-- <td class="uppercase">{{ obj.tpe_doc }}</td> -->
                        <td class="uppercase">{{ obj.journal }}</td>
                        <td>{{ obj.data_valeurs?.mt_ttc || obj.solde | number : ".2-2" }}</td>
                        <td>
                          <span *ngIf="obj.total_debit > 0">{{
                            obj.total_debit | number : ".2-2"
                            }}</span>
                        </td>
                        <td>
                          <span *ngIf="obj.total_credit > 0">{{
                            obj.total_credit | number : ".2-2"
                            }}</span>
                        </td>
                        <td class="uppercase">
                          {{ obj.data_etat?.etat=='draft'?'En attente':obj.data_etat?.etat=='sent'?'Envoyé à la  banque':obj.data_etat?.etat=='inpaye'?'Impayé':'Encaissé' }}
                        </td>
                      </tr>
                    </tbody>
                  </nz-table>
                </div>
              </div>
              <div class="border-0 p-1 flex gap-2 justify-end items-end">
                <form-item label="Debit">
                  <input [ngClass]="'bg-[#FF7B7B]'" nz-input readonly class="font-bold text-lg"
                    [ngModel]="calculSoldeFrs('solde',filtre.frs).debit | number : '.02-2'" />
                </form-item>
                <form-item label="Credit">
                  <input [ngClass]="'bg-[#A1D99B]'" nz-input readonly class="font-bold text-lg"
                    [ngModel]="calculSoldeFrs('solde',filtre.frs).credit | number : '.02-2'" />
                </form-item>
                <form-item label="Solde">
                  <input [ngClass]="
                      calculSoldeFrs('solde',filtre.frs).solde >= 0 ? 'bg-[#A1D99B]' : 'bg-[#FF7B7B] text-black' "
                    nz-input readonly class="font-bold text-lg"
                    [ngModel]="calculSoldeFrs('solde',filtre.frs).solde | number : '.02-2'" />
                </form-item>
              </div>
            </ng-container>
            <ng-template #AutresClients>
              <nz-table nzSize="small" #tablelisteVentes [nzData]="
                  getDataList('compta.liste_ecritures', [ 'tiers', '==',  extractIdTiers(filtre.client)   ]) "
                [nzLoading]="getDataString('abr.loading') == '1'">
                <thead>
                  <tr>
                    <th nzWidth="2vw"></th>
                    <th>Pièces</th>
                    <th>Type</th>
                    <th>Journal</th>
                    <!-- <th>Montant</th> -->
                    <th>Debit</th>
                    <th>Credit</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let obj of tablelisteVentes.data">
                    <td><label nz-checkbox [(ngModel)]="obj._chk"></label></td>
                    <td>
                      <div class="uppercase">
                        {{ obj.intitule || obj.mode_paiement }}
                      </div>
                      {{ obj.date_doc | date : "dd-MM-yyyy" }}
                    </td>
                    <td class="uppercase">{{ obj.tpe_doc }}</td>
                    <td class="uppercase">{{ obj.journal }}</td>
                    <!-- <td>{{ obj.data_valeurs?.mt_ttc || obj.solde | number : ".2-2" }}</td> -->
                    <td>
                      <span *ngIf="obj.total_debit > 0">{{
                        obj.total_debit | number : ".2-2"
                        }}</span>
                    </td>
                    <td>
                      <span *ngIf="obj.total_credit > 0">{{
                        obj.total_credit | number : ".2-2"
                        }}</span>
                    </td>
                  </tr>
                </tbody>
              </nz-table>
            </ng-template>
          </div>
        </div>
      </nz-tab>
      <nz-tab class="uppercase" [nzTitle]="ttlLub">
        <ng-template #ttlLub>
          <div class="font-bold uppercase">Avances</div>
        </ng-template>
        <div class="basis-[100%]">
          <div class="grid grid-cols-2 gap-2">
            <div>
              <nz-table nzSize="small" #tablelisteabrImpaye [nzData]="getBrAvonces()"
                [nzLoading]="getDataString('abr.loading') == '1'">
                <thead>
                  <tr>
                    <th nzWidth="2vw"></th>
                    <th>N°</th>
                    <th>Type</th>
                    <th>Montant TTC</th>
                    <th>M. Paiement</th>
                    <th>Avoir</th>
                    <th>Reste</th>
                    <th>Reste reel</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let obj of tablelisteabrImpaye.data"  [ngClass]="{'bg-red-50': obj?.is_avoir}">
                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''">
                      <span nz-checkbox (nzCheckedChange)="selctedAbr(obj)" [disabled]="obj?.reel_rest<=0"
                        [(ngModel)]="obj._chk"></span>
                    </td>
                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''"
                      class="uppercase">{{ obj.numero_interne }}</td>
                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''"
                      class="uppercase">{{ obj.tpe_doc }}</td>
                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''">{{
                      obj.data_valeurs?.mt_ttc || 0 | number :
                      ".2-2" }}</td>
                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''">{{
                      obj.data_paiement.montant_paiement | number :
                      ".2-2" }}</td>
                    <td
                      [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100 text-red-700':obj?.is_avoir? 'bg-red-100 text-red-700':'text-red-700'"
                      >-{{ obj.data_avoir.length>0? getMontantAvoir(obj):0 | number : ".2-2" }}</td>
                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''">{{
                      obj.data_paiement.reste | number : ".2-2" }}
                    </td>

                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''">{{
                      obj.reel_rest - (obj.data_avoir.length>0? getMontantAvoir(obj):0)| number : ".2-2" }}</td>
                    <td [class]="obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''">
                      <span nz-checkbox (nzCheckedChange)="selctedAbrAvr(obj)"
                        [disabled]="getDataList('achat.liste_selected_avr').length>=2 && !obj.ckavr"
                        [(ngModel)]="obj.ckavr"></span>
                    </td>
                  </tr>
                </tbody>
              </nz-table>
              <div class="basis-[80%] py-2">
                <a nz-button nzType="primary" class="uppercase float-right"
                  [disabled]="!achatCommercialPrivilege() || !getDataList('achat.liste_abr',['ckavr', '==', true]).length || getDataString('confirmabr.loading') == '1'"
                  (click)="majBr({avr: getDataList('achat.liste_selected_avr',['tpe_doc', '==', 'avrf']), br: getDataList('achat.liste_selected_avr',['tpe_doc', '==', 'abr']), tpe: 'frs'}, {tiers: this.filtre.frs, journal: ['ve', 'bq', 'ca', 'od'], avonce:false, withdet :true}) "
                  [nzLoading]="getDataString('confirmabr.loading') == '1'">
                  <div class="flex items-center gap-2">
                    <span>Confirmer</span>
                  </div>
                </a>
              </div>
            </div>
            <nz-table nzSize="small" #tablelisteEcrituresAvonce [nzData]="getEcrituresAvonces()"
              [nzLoading]="getDataString('abr.loading') == '1'">
              <thead>
                <tr>
                  <th nzWidth="2vw"></th>
                  <th>Pièces</th>
                  <th>Type</th>
                  <th>Journal</th>
                  <th>Montant</th>
                  <th>Debit</th>
                  <th>Credit</th>
                  <th>Reste</th>
                  <th>Etat</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let obj of tablelisteEcrituresAvonce.data"
                  [class]="obj?.data_etat?.etat=='inpaye'?'text-red-700':obj?.data_etat?.etat=='encaisse'?'text-green-600':''">
                  <td><label nz-checkbox [(ngModel)]="obj._chk"></label></td>
                  <td>
                    <div class="uppercase">{{ obj.intitule || obj.mode_paiement }}</div>
                    {{ ( obj.date_echeance? obj.date_echeance: obj.date_doc )| date : "dd-MM-yyyy"  }}
                  </td>
                  <td class="uppercase">{{ obj.tpe_doc }}</td>
                  <td class="uppercase">{{ obj.journal }}</td>
                  <td>{{ obj.data_valeurs?.mt_ttc || obj.solde | number : ".2-2" }}</td>
                  <td><span *ngIf="obj.total_debit>0">{{ obj.total_debit | number : ".2-2" }}</span></td>
                  <td><span *ngIf="obj.total_credit>0">{{ obj.total_credit | number : ".2-2" }}</span></td>
                  <td><span>{{ obj.data_regl_paiement.reste | number : ".2-2" }}</span></td>
                  <td class="uppercase">
                    <span *ngIf="obj?.data_etat?.etat">
                      {{ obj.data_etat.etat=='draft'?'En attente':obj.data_etat.etat=='sent'?'Envoyé à la  banque':obj.data_etat.etat=='inpaye'?'Impayé':obj.data_etat.etat=='compta'?'Comptabilisée':'Encaissé'
                      }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </div>
          <div class="basis-[80%] py-2">
            <a nz-button nzType="primary" class="uppercase float-right"
              [disabled]="!achatCommercialPrivilege() || !getDataList('achat.liste_abr', ['_chk', '==', true]).length || !(getDataList('compta.liste_ecritures', ['_chk', '==', true]).length == 1) || getDataString('confirmavc.loading') == '1'"
              (click)="majAvonce({bls: getDataList('achat.liste_selected_abr'), ecr: this.getDataList('compta.liste_ecritures', ['_chk', '==', true]), tpe: 'frs'}, this.filtre.frs)"
              [nzLoading]="getDataString('confirmavc.loading') == '1'">
              <div class="flex items-center gap-2">
                <span>Confirmer</span>
              </div>
            </a>
          </div>
        </div>
      </nz-tab>
      <nz-tab class="uppercase" [nzTitle]="titleLitrage">
        <ng-template #titleLitrage>
          <div class="font-bold uppercase">Historique de lettrege des reglements</div>
        </ng-template>
        <div class="basis-[100%]">
          <nz-table #nzTable [nzData]="getDataList('achat.liste_abr_history')" nzTableLayout="fixed"
            [nzLoading]="getDataString('abrh.loading') == '1'">
            <thead>
              <tr>
                <th nzWidth="50px"></th>
                <th>N°</th>
                <th>Date</th>
                <th>Montant TTC</th>
                <th>Montant TTC</th>
                <th>Reste</th>
                <th>Situation paiment</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let data of nzTable.data">
                <tr>
                  <td [nzExpand]="expandSet.has(data.id)" (nzExpandChange)="onExpandChange(data.id, $event)"></td>
                  <td>{{ data.numero_interne }}</td>
                  <td>{{ data.date_doc | date : "dd-MM-yyyy" }}</td>
                  <td>{{ data.data_valeurs.mt_ttc || 0 | number : ".2-2" }}</td>
                  <td>{{ data.data_paiement.montant_paiement | number : ".2-2" }}</td>
                  <td>{{ data.data_paiement.reste | number : ".2-2" }}</td>
                  <td class="uppercase">
                    <nz-tag
                      [nzColor]="data.data_paiement.status == 'En cours'?'orange':data.data_paiement.status == 'Impayé'?'red':'green'">{{data.data_paiement.status}}</nz-tag>
                  </td>
                  <td class="uppercase">
                    <a nz-button nzType="link" nz-popconfirm [nzPopconfirmTitle]="Messages.confirm_update_bl"
                      nzPopconfirmPlacement="bottom" [disabled]="!adminPrivilege()"  
                      [nzLoading]="getDataString('reloadbr.loading') == '1'"
                      (nzOnConfirm)="adminPrivilege() && ReloadBr(data, filtre.frs)">
                      <span nz-icon nzType="reload" nzTheme="outline"></span>
                    </a>                  
                  </td>
                </tr>
                <tr [nzExpand]="expandSet.has(data.id)">
                  <div class="block w-full space-y-4 overflow-x-auto rounded-lg border border-white-dark/20 p-4">
                    <ng-container *ngIf="data.data_avoir.length>0">
                      <ng-container *ngFor="let obj of data.data_avoir">
                        <div
                          class="bg-red-50 flex min-w-[625px] items-center justify-around rounded-xl p-3 font-semibold text-gray-500 shadow-[0_0_4px_2px_rgb(31_45_61_/_10%)] transition-all duration-300 hover:scale-[1.01] ">
                          <div> Avoir </div>
                          <div class="text-red-700"> -{{obj.mt | number : ".2-2" : "fr"}} DH </div>
                        </div>
                      </ng-container>
                    </ng-container>
                    <ng-container *ngFor="let obj of data.ecritures">
                      <div [ngClass]="obj.data_etat.etat == 'draft'?'bg-red-50':'bg-white '"
                        class="flex min-w-[625px] items-center justify-between rounded-xl p-3 font-semibold text-gray-500 shadow-[0_0_4px_2px_rgb(31_45_61_/_10%)] transition-all duration-300 hover:scale-[1.01] ">
                        <div>
                          {{ obj.date_doc | date : "dd-MM-yyyy" }}
                        </div>
                        <div> {{ obj.numero_externe || obj.intitule}} </div>
                        <div class="text-green-700"> {{ (obj.data_multiple_paiement.length>0 &&
                          getMtOperation(obj.data_multi_det, data )>0?getMtOperation(obj.data_multi_det, data ):
                          getMtOperation(obj.data_multi_det, data)==0?obj.solde: obj.solde) | number : ".2-2" : "fr"}}
                          DH
                        </div>
                        <div> {{ obj.solde | number : ".2-2" : "fr"}} DH </div>
                        <div> {{ obj.mode_paiement | lbl : "commun.liste_mode_paiement" : "code" : "label" }} </div>
                      </div>
                    </ng-container>
                  </div>
                </tr>
                <tr></tr>
              </ng-container>
            </tbody>
          </nz-table>
          <div class="border-0 p-1 flex gap-2 justify-end items-end">
            <div class="pt-2 w-auto">
              <a nz-button nzType="primary" [disabled]="!filtre.frs" nz-popover nzPopoverPlacement="top"
                style="width: 100%;" [nzPopoverContent]="formPrintdet" nzPopoverTrigger="click" class="">
                <span>Impression Historique de lettrage des reglements</span>
              </a>
              <ng-template #formPrintdet>
                <div class="w-[20vw] flex gap-2">
                  <form-item label="Date Début">
                    <nz-date-picker class="w-full" [(ngModel)]="filtre.date_debut"></nz-date-picker>
                  </form-item>
                  <form-item label="Date Fin">
                    <nz-date-picker class="w-full" [(ngModel)]="filtre.date_fin"></nz-date-picker>
                  </form-item>
                </div>
                <div class="text-right pt-3">
                  <button nz-button (click)="imprimerLet('page_compte_frs_let')">Imprimer</button>
                </div>
              </ng-template>
            </div>
          </div>
        </div>
      </nz-tab>
    </nz-tabset>
  </div>
</div>
<comp-form-paiement *ngIf="isFormVisible('form_paiement')"></comp-form-paiement>
<compte-fournisseur-print></compte-fournisseur-print>
<compte-frs-print-let></compte-frs-print-let>
<compte-fournisseur-print-det></compte-fournisseur-print-det>