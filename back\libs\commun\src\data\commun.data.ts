/* eslint-disable prettier/prettier */

export const commun_data = {
  liste_pole: [
    { code: 'carb', label: 'Carburant' },
    { code: 'lub', label: 'Lubrifiant' },
    { code: 'gaz', label: 'Gaz' },
    { code: 'ful', label: 'Fuel' },
    { code: 'em', label: 'Emballage' },
  ],
  liste_frais: [
    { code: 'ass', label: 'Assurance', tva: 1 },
    { code: 'trs', label: 'Transport', tva: 0.12 },
    { code: 'transit', label: 'Transitaire', tva: 1.2 },
    { code: 'douane', label: 'Douane', tva: 1.2 },
    { code: 'banque', label: 'Banque', tva: 1.1 },
    { code: 'autre', label: 'Autres', tva: 1 },
  ],
  liste_seas_vt: [
    { code: 'hv', label: 'Hiver' },
    { code: 'et', label: 'Été' },
  ],
  liste_sens_mvt: [
    { code: 'e', label: 'Entrée (Dem. Réaprv)' },
    { code: 's', label: 'Sortie' },
  ],
  liste_qualites: [
    { code: 'prem', label: 'Premium' },
    { code: '1p', label: '1er Prix' },
  ],
  liste_devises: [
    { code: 'eur', label: 'EUR' },
    { code: 'usd', label: 'USD' },
    { code: 'mad', label: 'MAD' },
  ],

  liste_mode_livraison: [
    { code: 'dom', label: 'à domicile' },
    { code: 'ret', label: "Retrait à l'entrepôt" },
  ],

  liste_type_avocat: [
    { code: 'ci', label: 'Chèques impayés' },
    { code: 'dd', label: "Dossiers Divers" },
  ],

  liste_tva: [
    { code: "7", label: "7%", val: 1.07 },
    { code: "10", label: '10%', val: 1.1 },
    { code: "13", label: '13%', val: 1.13 },
    { code: "14", label: '14%', val: 1.14 },
    { code: "20", label: "20%", val: 1.2 },
    { code: "12", label: "12%", val: 1.12 },
  ],

  liste_mois: [
    { code: '01', label: 'Janv.' },
    { code: '02', label: 'Févr.' },
    { code: '03', label: 'Mars' },
    { code: '04', label: 'Avr.' },
    { code: '05', label: 'Mai' },
    { code: '06', label: 'Juin' },
    { code: '07', label: 'Juil.' },
    { code: '08', label: 'Août' },
    { code: '09', label: 'Sep.' },
    { code: '10', label: 'Oct.' },
    { code: '11', label: 'Nov.' },
    { code: '12', label: 'Dec.' },
  ],

  // liste_operation_ca_bq: [
  //   // {
  //   //   code: 'reg_client',
  //   //   label: 'Réglement Client',
  //   //   natures: ['bq', 'ca'],
  //   //   contrepartie: ['vbl'],
  //   //   sens: 'e',
  //   // },
  //   // {
  //   //   code: 'alim_caisse',
  //   //   label: 'Alimentation Caisse',
  //   //   natures: ['ca'],
  //   //   sens: 'e',
  //   // },
  //   // {
  //   //   code: 'montant_a_verser_a_bq',
  //   //   label: 'Montant à verser à la Banque',
  //   //   natures: ['ca'],
  //   //   sens: 'e',
  //   // },
  //   // {
  //   //   code: 'montant_verser_a_bq',
  //   //   label: 'Montant verser à la Banque',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 's',
  //   // },
  //   // {
  //   //   code: 'remb_achat',
  //   //   label: 'Rembourssement Achat',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 'e',
  //   // },
  //   // {
  //   //   code: 'regl_frs',
  //   //   label: 'Paiement Achat',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 's',
  //   // },
  //   // {
  //   //   code: 'regl_frsv',
  //   //   label: 'Paiement commande vétements',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 's',
  //   // },
  //   // {
  //   //   code: 'regl_stav',
  //   //   label: 'Paiement vente vétements',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 'e',
  //   // },
  //   // {
  //   //   code: 'regl_invest',
  //   //   label: 'Paiement investissement',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 's',
  //   // },
  //   // {
  //   //   code: 'regl_reparation_vehicule',
  //   //   label: 'Réparation Véhicule',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 's',
  //   // },
  //   // {
  //   //   code: 'regl_location_vehicule',
  //   //   label: 'Location Camion',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 's',
  //   // },
  //   // {
  //   //   code: 'cap',
  //   //   label: 'Capital',
  //   //   natures: ['bq'],
  //   //   sens: 'e',
  //   // },
  //   // {
  //   //   code: 'frais_bancaire',
  //   //   label: 'Frais Bancaire',
  //   //   natures: ['bq', 'ca'],
  //   //   sens: 's',
  //   // },
  //   {
  //     code: 'frais_divers',
  //     label: 'Frais Divers',
  //     natures: ['bq', 'ca'],
  //     sens: 's',
  //   },
  //   {
  //     code: 'regl_com',
  //     label: 'Paiement commercial',
  //     natures: ['bq'],
  //     sens: 's',
  //   },
  //   {
  //     code: 'loyer',
  //     label: 'Loyer',
  //     natures: ['bq', 'ca'],
  //     sens: 's',
  //   },
  //   {
  //     code: 'rem_mat',
  //     label: 'Remboursements matériel',
  //     natures: ['bq', 'ca'],
  //     sens: 'e',
  //   },
  //   {
  //     code: 'imp_tx',
  //     label: 'IMPOTS & TAXES',
  //     natures: ['bq', 'ca'],
  //     sens: 's',
  //   },
  //   {
  //     code: 'cot_cnss',
  //     label: 'Cotisation CNSS',
  //     natures: ['bq'],
  //     sens: 's',
  //   },
  // ],
};
