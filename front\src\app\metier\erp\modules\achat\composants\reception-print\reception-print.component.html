<div class="hidden">
  <div [id]="code">
    <div *ngFor="let num_page of Object.keys(pages)" class="next-page relative h-[100vh] p-2">
      <div class="overflow-hidden flex items-end p-2 border-0">
        <div class="basis-[90%]">
          <div class="w-[50%] flex justify-center">
            <div>
              <img src="./assets/logo-top-petrol.png" />
            </div>
          </div>
        </div>
        <div class="pr-2 flex justify-end uppercase">
          {{ doc.date_doc | date : "dd-MM-yyyy" }}
        </div>
      </div>
      <div class="font-bold text-lg uppercase my-1 border-0 bg-yellow-50 pl-4">
        {{doc.is_avoir ? 'Avoir fournisseur ' : 'Bon de Réception N° '}} {{ doc.numero_interne }}
      </div>
      <div class="my-1 flex justify-between p-1">
        <div class="text-left basis-[40%] border-2 p-2 rounded-md">
          <div class="font-bold border-b-2 uppercase p-2">Fournisseur : <span class="font-normal">{{ doc.data_tiers?.nom
              }}</span></div>
          <div class="mt-2">
            <div class="space-y-2">
              <div class="uppercase">
                <div class="font-bold">ice : <span class="font-normal">{{ cls?.data_statut?.ice }}</span> </div>
                <div class="font-bold">Adresse : <span class="font-normal">{{ cls?.data_adresses['f']?.adre }}</span>
                </div>
                <div class="font-bold">téléphone : <span class="font-normal">{{ cls?.data_adresses['f']?.tel }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="text-left basis-[40%] border-2 p-2 rounded-md">
          <div class="font-bold border-b-2 uppercase p-2">Camion : <span class="font-normal">{{ doc.data_camion?.mat
              }}</span></div>
          <div class="mt-2">
            <div class="space-y-2">
              <div class="uppercase">
                <div class="font-bold">Chauffeur : <span class="font-normal">{{ doc.data_camion?.ch }}</span> </div>
                <div class="font-bold">téléphone : <span class="font-normal">{{ doc.data_camion?.tel }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="h-[65vh] border-2 p-2 relative">
        <div class="p-2 font-bold">
          <div class="flex gap-x-2 gap-y-1 bg-yellow-50">
            <div class="basis-[20%]">
              Référence
            </div>
            <div class="basis-[30%]">
              Désignation
            </div>
            <div class="basis-[10%]">
              Quantité
            </div>
            <div class="basis-[10%]" *ngIf="withPrice">
              Prix TTC
            </div>
            <div class="basis-[10%]" *ngIf="withPrice">TVA</div>
            <div class="basis-[10%]" *ngIf="withPrice">
              Total TTC
            </div>
          </div>
        </div>
        <div *ngFor="let lg of pages[num_page].lignes" class="p-2">
          <div class="flex gap-x-2 gap-y-1">
            <div class="basis-[20%]">
              {{ lg.data_produit?.code }}
            </div>
            <div class="basis-[30%]">
              {{ lg.data_produit?.libelle }}
            </div>
            <div class="basis-[10%]">
              {{ lg.data_qte?.abr }}
            </div>
            <div class="basis-[10%]" *ngIf="withPrice">
              {{ lg.data_pricing?.prix_ttc || 0 | trimDecimal }}
            </div>
            <div class="basis-[10%]" *ngIf="withPrice">
              {{ getDataList("commun.liste_tva", ['val','==',lg.data_pricing?.tva])[0]?.label }}
            </div>
            <div class="basis-[10%]" *ngIf="withPrice">
              {{ (lg.data_pricing?.prix_ttc*lg.data_qte?.abr || 0) |  trimDecimal }}
            </div>
          </div>
          <div class="flex justify-end p-4 absolute bottom-0" *ngIf="withPrice">
            <div class="bg-yellow-50 px-4 py-2 w-fit" *ngIf="doc.data_valeurs as total">
              <div class="font-bold mr-3 uppercase">Total HT : {{ (doc.is_avoir?- total.mt_ht : total.mt_ht) | trimDecimal}}</div>
              <div class="font-bold mr-3 uppercase">Total TTC : {{ (doc.is_avoir?- total.mt_ttc: total.mt_ttc) |trimDecimal}}</div>
            </div>
          </div>
        </div>
        <div *ngIf="doc.data_frais.lst as listeFrais" class="p-2">
          <ng-container *ngFor="let frais of listeFrais;let ndx = index">
            <ng-container *ngIf="getFraisRubrique(frais) as listeFrais_">
              <div class="flex gap-x-2 gap-y-1" *ngFor="let obj of listeFrais_">
                <div class="basis-[73%] uppercase">
                  {{ obj.lib || frais.label }}
                </div>
                <div class="basis-[10%]" *ngIf="withPrice">
                  {{ getDataList("commun.liste_tva", ['val','==',obj.tva])[0]?.label }}
                </div>
                <div class="basis-[10%]" *ngIf="withPrice">
                  {{ obj.mt || 0 | trimDecimal }}
                </div>
              </div>
            </ng-container>
          </ng-container>
          <div class="flex justify-end p-4 absolute bottom-0" *ngIf="withPrice">
            <div class="bg-yellow-50 px-4 py-2 w-fit" *ngIf="doc.data_valeurs as total">
              <div class="font-bold mr-3 uppercase">Total HT : {{( doc.is_avoir?- total.mt_ht : total.mt_ht) | trimDecimal}}</div>
              <div class="font-bold mr-3 uppercase">Total TTC : {{ (doc.is_avoir?- total.mt_ttc: total.mt_ttc) | trimDecimal }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-end">Page : {{ num_page }} / {{ nbPages }}</div>
      <div class="text-center mt-4">
        TOP PETROL - BUSINESS CENTRE YASMINA 1ER ETG AIN SEBAA CASABLANCA
      </div>
      <div class="text-center mt-1">IF:1687898 - ICE: 000164488000066 - Patente: 31990429 - FIX: 0522343920</div>
    </div>
  </div>
</div>