import { Component, OnInit } from '@angular/core';
import { AppService } from 'src/app/core/services/app.service';
import { BackEndService } from 'src/app/store/services/back-end.service';
import { TresorComposant } from '../../features/tresor.composant';

@Component({
  selector: 'comp-page-banque',
  templateUrl: './page-banque.component.html',
  styleUrls: ['./page-banque.component.scss']
})
export class PageBanqueComponent extends TresorComposant implements OnInit {
  constructor(
    protected override app: AppService,
    protected override backendService: BackEndService
  ) {
    super(app, backendService);
  }
  jours: any[] = [];
  years: number[] = [];
  cumul_journee = 0;
  ngOnInit(): void {
    this.setTitre('Gestion des Comptes Bancaire');
    this.setPlan('saisie')
    const currentYear = new Date().getFullYear();
    const startYear = 2023;
    this.vars.selected_year = currentYear
    for (let year = startYear; year <= currentYear; year++) {
      this.years.push(year);
    }
    this.getCumulJournee()
    this.getTotalSolde(this.vars.journal)
  }

  setPlan(pln: string) {
    this.vars.plan = pln
    if (this.vars.mois && this.vars.plan == 'saisie')
      this.traiteEcritureBanque(this.Actions.Get, false, this.vars.journal, { mois: this.vars.mois, annee: this.vars.selected_year , sh: this.getDataString('bq.intitule') })
  }
  handleIsValidChange(value: boolean) {
    this.vars.isValid = value;
    this.traiteEcritureBanque(this.Actions.Get, true, this.vars.journal, {annee: this.vars.selected_year ,mois: this.vars.mois, sh: this.getDataString('bq.intitule') }, value)
  }
  selectMois(mois: any) {
    this.vars.mois = mois;
    this.jours = this.listeJoursDuMois(this.vars.selected_year, +this.vars.mois.code, this.getConfig('dts'))
    this.jours = this.jours.reduce((acc, currentArray) => acc.concat(currentArray), []);
    if (this.vars.plan == 'saisie') {
      this.traiteEcritureBanque(this.Actions.Get, false, this.vars.journal, { mois: this.vars.mois, annee: this.vars.selected_year , sh: this.getDataString('bq.intitule')})
    }
    else if (this.vars.plan == 'suivi'){
      console.log(this.getDataString('bq.intitule'));
      this.traiteEcritureBanque(this.Actions.Get, true, this.vars.journal, { mois: this.vars.mois, annee: this.vars.selected_year, sh: this.getDataString('bq.intitule')}, this.vars.isValid)
    }
  }
}