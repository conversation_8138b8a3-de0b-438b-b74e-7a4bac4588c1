{"ast": null, "code": "import { TresorComposant } from '../../features/tresor.composant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/app.service\";\nimport * as i2 from \"src/app/store/services/back-end.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"ng-zorro-antd/icon\";\nimport * as i6 from \"ng-zorro-antd/input\";\nimport * as i7 from \"ng-zorro-antd/popconfirm\";\nimport * as i8 from \"ng-zorro-antd/checkbox\";\nimport * as i9 from \"ng-zorro-antd/date-picker\";\nimport * as i10 from \"ng-zorro-antd/select\";\nimport * as i11 from \"ng-zorro-antd/popover\";\nimport * as i12 from \"ng-zorro-antd/table\";\nimport * as i13 from \"ng-zorro-antd/tabs\";\nimport * as i14 from \"ng-zorro-antd/tag\";\nimport * as i15 from \"ng-zorro-antd/button\";\nimport * as i16 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i17 from \"ng-zorro-antd/core/wave\";\nimport * as i18 from \"../../../../../../../../shared/composants/form-item/form-item.component\";\nimport * as i19 from \"../../composants/form-paiement/form-paiement.component\";\nimport * as i20 from \"../compte-fournisseur-print/compte-fournisseur-print.component\";\nimport * as i21 from \"../compte-frs-print-let/compte-frs-print-let.component\";\nimport * as i22 from \"../compte-fournisseur-print-det/compte-fournisseur-print-det.component\";\nimport * as i23 from \"../../../../../../../../shared/pipes/lab-by-id.pipe\";\nfunction CompteFournisseurComponent_nz_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 48);\n  }\n  if (rf & 2) {\n    const frs_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", frs_r25.nom_tiers)(\"nzValue\", \"frs_\" + frs_r25.id);\n  }\n}\nfunction CompteFournisseurComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"form-item\", 50)(2, \"nz-date-picker\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_10_Template_nz_date_picker_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.filtre.date_debut = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"form-item\", 52)(4, \"nz-date-picker\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_10_Template_nz_date_picker_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.filtre.date_fin = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"label\", 53);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_10_Template_label_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.checked = $event);\n    });\n    i0.ɵɵtext(6, \"Avec r\\u00E9glement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 54)(8, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function CompteFournisseurComponent_ng_template_10_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.imprimer());\n    });\n    i0.ɵɵtext(9, \"Imprimer\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filtre.date_debut);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filtre.date_fin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.checked);\n  }\n}\nfunction CompteFournisseurComponent_ng_template_16_nz_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 48);\n  }\n  if (rf & 2) {\n    const tpe_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", tpe_r32.label)(\"nzValue\", tpe_r32.code);\n  }\n}\nfunction CompteFournisseurComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"form-item\", 50)(2, \"nz-date-picker\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_16_Template_nz_date_picker_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.filtre.date_debut = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"form-item\", 52)(4, \"nz-date-picker\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_16_Template_nz_date_picker_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.filtre.date_fin = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"form-item\", 57)(7, \"nz-select\", 4);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_16_Template_nz_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.filtre.pole = $event);\n    });\n    i0.ɵɵtemplate(8, CompteFournisseurComponent_ng_template_16_nz_option_8_Template, 1, 2, \"nz-option\", 5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 54)(10, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function CompteFournisseurComponent_ng_template_16_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.imprimerDet(\"page_compte_frs_det\"));\n    });\n    i0.ɵɵtext(11, \"Imprimer\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.filtre.date_debut);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.filtre.date_fin);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.filtre.pole);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.liste_pole);\n  }\n}\nfunction CompteFournisseurComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1, \"Compte Fournisseur\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"bg-red-50\": a0\n  };\n};\nfunction CompteFournisseurComponent_nz_table_35_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 61)(1, \"td\")(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 62);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const obj_r41 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, obj_r41 == null ? null : obj_r41.is_avoir));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(obj_r41.numero_interne);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(obj_r41.numero_externe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", obj_r41.tpe_doc, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 6, obj_r41.date_doc, \"dd-MM-yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(obj_r41.is_avoir ? -obj_r41.data_valeurs.mt_ttc : i0.ɵɵpipeBind3(14, 9, obj_r41.data_valeurs.mt_ttc || 0, \".2-2\", \"fr\"));\n  }\n}\nfunction CompteFournisseurComponent_nz_table_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nz-table\", 59, 60)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00B0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Montant TTC\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, CompteFournisseurComponent_nz_table_35_tr_13_Template, 15, 15, \"tr\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const listeDocs_r38 = ctx.ngIf;\n    const _r39 = i0.ɵɵreference(1);\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzLoading\", ctx_r7.getDataString(\"abr.loading\") == \"1\")(\"nzData\", listeDocs_r38);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", _r39.data);\n  }\n}\nfunction CompteFournisseurComponent_tr_54_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const obj_r42 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, obj_r42.total_debit, \".2-2\"));\n  }\n}\nfunction CompteFournisseurComponent_tr_54_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const obj_r42 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, obj_r42.total_credit, \".2-2\"));\n  }\n}\nfunction CompteFournisseurComponent_tr_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 63);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 64);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 63);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, CompteFournisseurComponent_tr_54_span_13_Template, 3, 4, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtemplate(15, CompteFournisseurComponent_tr_54_span_15_Template, 3, 4, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 63);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const obj_r42 = ctx.$implicit;\n    i0.ɵɵclassMap((obj_r42 == null ? null : obj_r42.data_etat == null ? null : obj_r42.data_etat.etat) == \"inpaye\" ? \"text-red-700\" : (obj_r42 == null ? null : obj_r42.data_etat == null ? null : obj_r42.data_etat.etat) == \"encaisse\" ? \"text-green-600\" : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", obj_r42.intitule || obj_r42.mode_paiement, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", obj_r42.date_echeance ? obj_r42.date_echeance : i0.ɵɵpipeBind2(6, 9, obj_r42.date_doc, \"dd-MM-yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(obj_r42.journal);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 12, (obj_r42.data_valeurs == null ? null : obj_r42.data_valeurs.mt_ttc) || obj_r42.solde, \".2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", obj_r42.total_debit > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", obj_r42.total_credit > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (obj_r42.data_etat == null ? null : obj_r42.data_etat.etat) == \"draft\" ? \"En attente\" : (obj_r42.data_etat == null ? null : obj_r42.data_etat.etat) == \"sent\" ? \"Envoy\\u00E9 \\u00E0 la banque\" : (obj_r42.data_etat == null ? null : obj_r42.data_etat.etat) == \"inpaye\" ? \"Impay\\u00E9\" : \"Encaiss\\u00E9\", \" \");\n  }\n}\nfunction CompteFournisseurComponent_ng_template_65_tr_16_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const obj_r49 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, obj_r49.total_debit, \".2-2\"));\n  }\n}\nfunction CompteFournisseurComponent_ng_template_65_tr_16_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const obj_r49 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, obj_r49.total_credit, \".2-2\"));\n  }\n}\nfunction CompteFournisseurComponent_ng_template_65_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"label\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_65_tr_16_Template_label_ngModelChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const obj_r49 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(obj_r49._chk = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 63);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 63);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, CompteFournisseurComponent_ng_template_65_tr_16_span_13_Template, 3, 4, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtemplate(15, CompteFournisseurComponent_ng_template_65_tr_16_span_15_Template, 3, 4, \"span\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const obj_r49 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", obj_r49._chk);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", obj_r49.intitule || obj_r49.mode_paiement, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 7, obj_r49.date_doc, \"dd-MM-yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(obj_r49.tpe_doc);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(obj_r49.journal);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", obj_r49.total_debit > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", obj_r49.total_credit > 0);\n  }\n}\nconst _c1 = function (a2) {\n  return [\"tiers\", \"==\", a2];\n};\nfunction CompteFournisseurComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nz-table\", 23, 65)(2, \"thead\")(3, \"tr\");\n    i0.ɵɵelement(4, \"th\", 35);\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Pi\\u00E8ces\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Journal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Debit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Credit\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, CompteFournisseurComponent_ng_template_65_tr_16_Template, 16, 10, \"tr\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r47 = i0.ɵɵreference(1);\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzData\", ctx_r11.getDataList(\"compta.liste_ecritures\", i0.ɵɵpureFunction1(3, _c1, ctx_r11.extractIdTiers(ctx_r11.filtre.client))))(\"nzLoading\", ctx_r11.getDataString(\"abr.loading\") == \"1\");\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", _r47.data);\n  }\n}\nfunction CompteFournisseurComponent_ng_template_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1, \"Avances\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompteFournisseurComponent_tr_94_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 61)(1, \"td\")(2, \"span\", 67);\n    i0.ɵɵlistener(\"nzCheckedChange\", function CompteFournisseurComponent_tr_94_Template_span_nzCheckedChange_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r58);\n      const obj_r56 = restoredCtx.$implicit;\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.selctedAbr(obj_r56));\n    })(\"ngModelChange\", function CompteFournisseurComponent_tr_94_Template_span_ngModelChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r58);\n      const obj_r56 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(obj_r56._chk = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\")(23, \"span\", 67);\n    i0.ɵɵlistener(\"nzCheckedChange\", function CompteFournisseurComponent_tr_94_Template_span_nzCheckedChange_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r58);\n      const obj_r56 = restoredCtx.$implicit;\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.selctedAbrAvr(obj_r56));\n    })(\"ngModelChange\", function CompteFournisseurComponent_tr_94_Template_span_ngModelChange_23_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r58);\n      const obj_r56 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(obj_r56.ckavr = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const obj_r56 = ctx.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c0, obj_r56 == null ? null : obj_r56.is_avoir));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", (obj_r56 == null ? null : obj_r56.reel_rest) <= 0)(\"ngModel\", obj_r56._chk);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(obj_r56.numero_interne);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(obj_r56.tpe_doc);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 30, (obj_r56.data_valeurs == null ? null : obj_r56.data_valeurs.mt_ttc) || 0, \".2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 33, obj_r56.data_paiement.montant_paiement, \".2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100 text-red-700\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100 text-red-700\" : \"text-red-700\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"-\", obj_r56.data_avoir.length > 0 ? ctx_r15.getMontantAvoir(obj_r56) : i0.ɵɵpipeBind2(15, 36, 0, \".2-2\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(18, 39, obj_r56.data_paiement.reste, \".2-2\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(21, 42, obj_r56.reel_rest - (obj_r56.data_avoir.length > 0 ? ctx_r15.getMontantAvoir(obj_r56) : 0), \".2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap((obj_r56 == null ? null : obj_r56.reel_rest) <= 0 && (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-green-100\" : (obj_r56 == null ? null : obj_r56.is_avoir) ? \"bg-red-100\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r15.getDataList(\"achat.liste_selected_avr\").length >= 2 && !obj_r56.ckavr)(\"ngModel\", obj_r56.ckavr);\n  }\n}\nfunction CompteFournisseurComponent_tr_122_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const obj_r62 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, obj_r62.total_debit, \".2-2\"));\n  }\n}\nfunction CompteFournisseurComponent_tr_122_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const obj_r62 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, obj_r62.total_credit, \".2-2\"));\n  }\n}\nfunction CompteFournisseurComponent_tr_122_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const obj_r62 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", obj_r62.data_etat.etat == \"draft\" ? \"En attente\" : obj_r62.data_etat.etat == \"sent\" ? \"Envoy\\u00E9 \\u00E0 la banque\" : obj_r62.data_etat.etat == \"inpaye\" ? \"Impay\\u00E9\" : obj_r62.data_etat.etat == \"compta\" ? \"Comptabilis\\u00E9e\" : \"Encaiss\\u00E9\", \" \");\n  }\n}\nfunction CompteFournisseurComponent_tr_122_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"label\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_tr_122_Template_label_ngModelChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r70);\n      const obj_r62 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(obj_r62._chk = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 63);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 63);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 63);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtemplate(17, CompteFournisseurComponent_tr_122_span_17_Template, 3, 4, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtemplate(19, CompteFournisseurComponent_tr_122_span_19_Template, 3, 4, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\", 63);\n    i0.ɵɵtemplate(25, CompteFournisseurComponent_tr_122_span_25_Template, 2, 1, \"span\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const obj_r62 = ctx.$implicit;\n    i0.ɵɵclassMap((obj_r62 == null ? null : obj_r62.data_etat == null ? null : obj_r62.data_etat.etat) == \"inpaye\" ? \"text-red-700\" : (obj_r62 == null ? null : obj_r62.data_etat == null ? null : obj_r62.data_etat.etat) == \"encaisse\" ? \"text-green-600\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", obj_r62._chk);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(obj_r62.intitule || obj_r62.mode_paiement);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", obj_r62.date_echeance ? obj_r62.date_echeance : i0.ɵɵpipeBind2(7, 12, i0.ɵɵpipeBind2(8, 15, obj_r62.date_doc, \"dd-MM-yyyy\"), \"dd-MM-yyyy\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(obj_r62.tpe_doc);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(obj_r62.journal);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 18, (obj_r62.data_valeurs == null ? null : obj_r62.data_valeurs.mt_ttc) || obj_r62.solde, \".2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", obj_r62.total_debit > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", obj_r62.total_credit > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, obj_r62.data_regl_paiement.reste, \".2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", obj_r62 == null ? null : obj_r62.data_etat == null ? null : obj_r62.data_etat.etat);\n  }\n}\nfunction CompteFournisseurComponent_ng_template_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1, \"Historique de lettrege des reglements\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompteFournisseurComponent_ng_container_151_ng_container_25_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 74)(2, \"div\");\n    i0.ɵɵtext(3, \" Avoir \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 75);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const obj_r75 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" -\", i0.ɵɵpipeBind3(6, 1, obj_r75.mt, \".2-2\", \"fr\"), \" DH \");\n  }\n}\nfunction CompteFournisseurComponent_ng_container_151_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CompteFournisseurComponent_ng_container_151_ng_container_25_ng_container_1_Template, 7, 5, \"ng-container\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const data_r71 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", data_r71.data_avoir);\n  }\n}\nfunction CompteFournisseurComponent_ng_container_151_ng_container_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 76)(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 77);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"lbl\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const obj_r77 = ctx.$implicit;\n    const data_r71 = i0.ɵɵnextContext().$implicit;\n    const ctx_r73 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", obj_r77.data_etat.etat == \"draft\" ? \"bg-red-50\" : \"bg-white \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 6, obj_r77.date_doc, \"dd-MM-yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", obj_r77.numero_externe || obj_r77.intitule, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(9, 9, obj_r77.data_multiple_paiement.length > 0 && ctx_r73.getMtOperation(obj_r77.data_multi_det, data_r71) > 0 ? ctx_r73.getMtOperation(obj_r77.data_multi_det, data_r71) : ctx_r73.getMtOperation(obj_r77.data_multi_det, data_r71) == 0 ? obj_r77.solde : obj_r77.solde, \".2-2\", \"fr\"), \" DH \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(12, 13, obj_r77.solde, \".2-2\", \"fr\"), \" DH \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(15, 17, obj_r77.mode_paiement, \"commun.liste_mode_paiement\", \"code\", \"label\"), \" \");\n  }\n}\nfunction CompteFournisseurComponent_ng_container_151_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\", 68);\n    i0.ɵɵlistener(\"nzExpandChange\", function CompteFournisseurComponent_ng_container_151_Template_td_nzExpandChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const data_r71 = restoredCtx.$implicit;\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.onExpandChange(data_r71.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 63)(18, \"nz-tag\", 69);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"td\", 63)(21, \"a\", 70);\n    i0.ɵɵlistener(\"nzOnConfirm\", function CompteFournisseurComponent_ng_container_151_Template_a_nzOnConfirm_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const data_r71 = restoredCtx.$implicit;\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.adminPrivilege() && ctx_r81.ReloadBr(data_r71, ctx_r81.filtre.frs));\n    });\n    i0.ɵɵelement(22, \"span\", 71);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"tr\", 72)(24, \"div\", 73);\n    i0.ɵɵtemplate(25, CompteFournisseurComponent_ng_container_151_ng_container_25_Template, 2, 1, \"ng-container\", 47);\n    i0.ɵɵtemplate(26, CompteFournisseurComponent_ng_container_151_ng_container_26_Template, 16, 22, \"ng-container\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(27, \"tr\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const data_r71 = ctx.$implicit;\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzExpand\", ctx_r21.expandSet.has(data_r71.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r71.numero_interne);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 14, data_r71.date_doc, \"dd-MM-yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 17, data_r71.data_valeurs.mt_ttc || 0, \".2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 20, data_r71.data_paiement.montant_paiement, \".2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 23, data_r71.data_paiement.reste, \".2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"nzColor\", data_r71.data_paiement.status == \"En cours\" ? \"orange\" : data_r71.data_paiement.status == \"Impay\\u00E9\" ? \"red\" : \"green\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(data_r71.data_paiement.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzPopconfirmTitle\", ctx_r21.Messages.confirm_update_bl)(\"disabled\", !ctx_r21.adminPrivilege())(\"nzLoading\", ctx_r21.getDataString(\"reloadbr.loading\") == \"1\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzExpand\", ctx_r21.expandSet.has(data_r71.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", data_r71.data_avoir.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", data_r71.ecritures);\n  }\n}\nfunction CompteFournisseurComponent_ng_template_157_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"form-item\", 50)(2, \"nz-date-picker\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_157_Template_nz_date_picker_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.filtre.date_debut = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"form-item\", 52)(4, \"nz-date-picker\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_ng_template_157_Template_nz_date_picker_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.filtre.date_fin = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 54)(6, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function CompteFournisseurComponent_ng_template_157_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.imprimerLet(\"page_compte_frs_let\"));\n    });\n    i0.ɵɵtext(7, \"Imprimer\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.filtre.date_debut);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.filtre.date_fin);\n  }\n}\nfunction CompteFournisseurComponent_comp_form_paiement_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"comp-form-paiement\");\n  }\n}\nconst _c2 = function () {\n  return [\"ckavr\", \"==\", true];\n};\nconst _c3 = function () {\n  return [\"_chk\", \"==\", true];\n};\nexport class CompteFournisseurComponent extends TresorComposant {\n  onExpandChange(id, checked) {\n    if (checked) {\n      this.expandSet.add(id);\n    } else {\n      this.expandSet.delete(id);\n    }\n  }\n  constructor(app, backendService, datePipe) {\n    super(app, backendService);\n    this.app = app;\n    this.backendService = backendService;\n    this.datePipe = datePipe;\n    this.expandSet = new Set();\n    this.checked = true;\n    this.liste_pole = [{\n      code: 'lub',\n      label: 'Lubrifiant'\n    }, {\n      code: 'carb',\n      label: 'Carburant'\n    }];\n  }\n  ngOnInit() {\n    this.setTitre('Gestion des comptes fournisseurs');\n  }\n  getMtOperation(data, current_data) {\n    if (data) {\n      const montant = data.find(d => d.id == current_data.id);\n      if (montant) return montant.montant || 0;\n    }\n    return 0;\n  }\n  selectFournisseur() {\n    this.setDataString('abr.loading', '1');\n    this.setDataString('abrh.loading', '1');\n    this.setDataList('achat.liste_selected_avr', []);\n    if (this.filtre.frs != undefined) {\n      this.getEcriture(this.filtre.frs, ['ve', 'bq', 'ca', 'od'], false, true);\n      this.getReceptionHistory(this.filtre.frs.split('_')[1], false);\n    }\n  }\n  getEcrituresAvonces() {\n    return this.getDataList('compta.liste_ecritures').filter(ecriture => ecriture.tpe_doc === 'regl_frs' && ecriture.data_regl_paiement.reste > 0 && ecriture.data_paiement == null && ecriture.data_tiers.tpe_tiers == this.filtre?.frs?.split('_')[0]) || [];\n  }\n  getBrAvonces() {\n    return this.getDataList('achat.liste_abr').filter(br => (br.data_paiement.status == 'Impayé' || br.data_paiement.status == 'En cours') && br.reel_rest >= 0);\n  }\n  reste() {\n    return this.getDataList('achat.liste_abr').filter(vbl => vbl._chk == true).reduce((acc, vbl) => acc + vbl.data_paiement.reste, 0);\n  }\n  montant() {\n    return this.getEcrituresAvonces().filter(vbl => vbl._chk == true).reduce((acc, vbl) => acc + vbl.solde, 0);\n  }\n  getFrs(cl_data) {\n    return this.getDataList(`tiers.liste_${cl_data.split('_')[0]}`).find(cl => cl.id == cl_data.split('_')[1]);\n  }\n  selctedAbr(bl) {\n    if (bl._chk) {\n      this.setDataList('achat.liste_selected_abr', this.getDataList('achat.liste_selected_abr').concat(bl));\n    } else {\n      this.getDataList('achat.liste_selected_abr').splice(this.getDataList('achat.liste_selected_abr').indexOf(bl), 1);\n    }\n  }\n  selctedAbrAvr(bl) {\n    if (bl.ckavr) {\n      this.setDataList('achat.liste_selected_avr', this.getDataList('achat.liste_selected_avr').concat(bl));\n    } else {\n      this.getDataList('achat.liste_selected_avr').splice(this.getDataList('achat.liste_selected_avr').indexOf(bl), 1);\n    }\n  }\n  imprimer() {\n    this.filtre.date_debut = this.datePipe.transform(this.filtre.date_debut, 'yyyy-MM-dd');\n    this.filtre.date_fin = this.datePipe.transform(this.filtre.date_fin, 'yyyy-MM-dd');\n    const frs = this.getDataList(`tiers.liste_frs`).find(f => f.id == this.extractIdTiers(this.filtre.frs));\n    var ecritures = this.getDataList('compta.liste_ecritures', ['tiers', '==', this.extractIdTiers(this.filtre.frs)]);\n    ecritures = ecritures.filter(ecr => new Date(ecr.date_doc) >= new Date(this.filtre.date_debut) && new Date(ecr.date_doc) <= new Date(this.filtre.date_fin));\n    var abr = this.getDataList('achat.liste_abr', ['tiers', '==', this.extractIdTiers(this.filtre.frs)]);\n    abr = this.getDataList('achat.liste_abr').filter(ab => new Date(ab.date_doc) >= new Date(this.filtre.date_debut) && new Date(ab.date_doc) <= new Date(this.filtre.date_fin));\n    if (this.checked) {\n      ecritures = ecritures.concat(abr);\n    } else {\n      ecritures = abr;\n    }\n    this.send2Printer({\n      code: 'page_compte_fournisseur',\n      data: {\n        frs,\n        ecritures,\n        abr,\n        checked: this.checked,\n        date_debut: this.filtre.date_debut,\n        date_fin: this.filtre.date_fin,\n        solde: this.calculSoldePrintFrs().solde,\n        debit: this.calculSoldePrintFrs().debit,\n        credit: this.calculSoldePrintFrs().credit\n      }\n    });\n  }\n  calculSoldePrintFrs() {\n    let solde = 0;\n    let debit = 0;\n    let credit = 0;\n    const frs = this.getDataList(`tiers.liste_frs`).find(f => f.id == this.extractIdTiers(this.filtre.frs));\n    var ecritures = this.getDataList('compta.liste_ecritures', ['tiers', '==', this.extractIdTiers(this.filtre.frs)]);\n    ecritures = ecritures.filter(ecr => new Date(ecr.date_doc) >= new Date(this.filtre.date_debut) && new Date(ecr.date_doc) <= new Date(this.filtre.date_fin));\n    var abr = this.getDataList('achat.liste_abr', ['tiers', '==', this.extractIdTiers(this.filtre.frs)]);\n    abr = this.getDataList('achat.liste_abr').filter(ab => new Date(ab.date_doc) >= new Date(this.filtre.date_debut) && new Date(ab.date_doc) <= new Date(this.filtre.date_fin));\n    if (ecritures) {\n      ecritures.forEach(ecriture => {\n        debit += ecriture.total_debit;\n        credit += ecriture.total_credit;\n      });\n    }\n    abr.forEach(abr => {\n      if (!abr.is_avoir) {\n        credit += abr.data_valeurs.mt_ttc || 0;\n      } else {\n        debit += abr.data_valeurs.mt_ttc || 0;\n      }\n    });\n    return {\n      debit,\n      credit,\n      solde: debit - credit\n    };\n  }\n  imprimerLet(code) {\n    this.filtre.date_debut = this.datePipe.transform(this.filtre.date_debut, 'yyyy-MM-dd');\n    this.filtre.date_fin = this.datePipe.transform(this.filtre.date_fin, 'yyyy-MM-dd');\n    const frs = this.getDataList(`tiers.liste_frs`).find(f => f.id == this.extractIdTiers(this.filtre.frs));\n    if (frs) {\n      let abrs = this.getDataList('achat.liste_abr_history').filter(ecr => new Date(ecr.date_doc) >= new Date(this.filtre.date_debut) && new Date(ecr.date_doc) <= new Date(this.filtre.date_fin));\n      this.send2Printer({\n        code,\n        data: {\n          ecritures: abrs,\n          frs,\n          date_debut: this.filtre.date_debut,\n          date_fin: this.filtre.date_fin\n        }\n      });\n    }\n  }\n  imprimerDet(code) {\n    this.filtre.date_debut = this.datePipe.transform(this.filtre.date_debut, 'yyyy-MM-dd');\n    this.filtre.date_fin = this.datePipe.transform(this.filtre.date_fin, 'yyyy-MM-dd');\n    const frs = this.getDataList(`tiers.liste_frs`).find(f => f.id == this.extractIdTiers(this.filtre.frs));\n    if (frs) {\n      let abr = this.getDataList('achat.liste_abr', ['tiers', '==', this.extractIdTiers(this.filtre.frs)]).filter(ecr => new Date(ecr.date_doc) >= new Date(this.filtre.date_debut) && new Date(ecr.date_doc) <= new Date(this.filtre.date_fin));\n      this.send2Printer({\n        code,\n        data: {\n          ecritures: abr,\n          frs,\n          date_debut: this.filtre.date_debut,\n          date_fin: this.filtre.date_fin,\n          pole: this.filtre.pole || 'lub'\n        }\n      });\n    }\n  }\n  getMontantAvoir(obj) {\n    let montant = 0;\n    for (let o of obj.data_avoir) {\n      montant += o.mt || 0;\n    }\n    return montant;\n  }\n}\nCompteFournisseurComponent.ɵfac = function CompteFournisseurComponent_Factory(t) {\n  return new (t || CompteFournisseurComponent)(i0.ɵɵdirectiveInject(i1.AppService), i0.ɵɵdirectiveInject(i2.BackEndService), i0.ɵɵdirectiveInject(i3.DatePipe));\n};\nCompteFournisseurComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CompteFournisseurComponent,\n  selectors: [[\"comp-compte-fournisseur\"]],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 163,\n  vars: 48,\n  consts: [[1, \"panel\", \"min-h-[80vh]\"], [1, \"flex\", \"gap-2\", \"justify-between\", \"w-full\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"label\", \"Fournisseur\", 1, \"w-[20%]\", \"align-middle\"], [\"nzAllowClear\", \"\", \"nzShowSearch\", \"\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [3, \"nzLabel\", \"nzValue\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-auto\", \"self-end\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nz-popover\", \"\", \"nzPopoverPlacement\", \"bottom\", \"nzPopoverTrigger\", \"click\", 1, \"\", 3, \"disabled\", \"nzPopoverContent\"], [\"formPrint\", \"\"], [1, \"pt-2\", \"self-end\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nz-popover\", \"\", \"nzPopoverPlacement\", \"top\", \"nzPopoverTrigger\", \"click\", 1, \"\", 2, \"width\", \"100%\", 3, \"disabled\", \"nzPopoverContent\"], [\"formPrintlet\", \"\"], [\"nz-button\", \"\", \"nzType\", \"primary\", 1, \"uppercase\", \"float-right\", 3, \"disabled\", \"click\"], [1, \"flex\", \"items-center\", \"gap-2\"], [\"nz-icon\", \"\", \"nzType\", \"plus\", \"nzTheme\", \"outline\"], [1, \"uppercase\", 3, \"nzTitle\"], [\"ttlProduit\", \"\"], [1, \"basis-[80%]\"], [1, \"basis-[100%]\"], [1, \"flex\", \"gap-2\"], [1, \"w-[50%]\"], [1, \"h-[60vh]\"], [\"nzSize\", \"small\", 3, \"nzLoading\", \"nzData\", 4, \"ngIf\"], [\"nzSize\", \"small\", 3, \"nzData\", \"nzLoading\"], [\"tablelisteEcritures\", \"\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"border-0\", \"p-1\", \"flex\", \"gap-2\", \"justify-end\", \"items-end\"], [\"label\", \"Debit\"], [\"nz-input\", \"\", \"readonly\", \"\", 1, \"font-bold\", \"text-lg\", 3, \"ngClass\", \"ngModel\"], [\"label\", \"Credit\"], [\"label\", \"Solde\"], [\"AutresClients\", \"\"], [\"ttlLub\", \"\"], [1, \"grid\", \"grid-cols-2\", \"gap-2\"], [\"tablelisteabrImpaye\", \"\"], [\"nzWidth\", \"2vw\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"basis-[80%]\", \"py-2\"], [\"nz-button\", \"\", \"nzType\", \"primary\", 1, \"uppercase\", \"float-right\", 3, \"disabled\", \"nzLoading\", \"click\"], [\"tablelisteEcrituresAvonce\", \"\"], [\"titleLitrage\", \"\"], [\"nzTableLayout\", \"fixed\", 3, \"nzData\", \"nzLoading\"], [\"nzTable\", \"\"], [\"nzWidth\", \"50px\"], [4, \"ngFor\", \"ngForOf\"], [1, \"pt-2\", \"w-auto\"], [\"formPrintdet\", \"\"], [4, \"ngIf\"], [3, \"nzLabel\", \"nzValue\"], [1, \"w-[20vw]\", \"flex\", \"gap-2\"], [\"label\", \"Date D\\u00E9but\"], [1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"label\", \"Date Fin\"], [\"nz-checkbox\", \"\", 1, \"pt-3\", 3, \"ngModel\", \"ngModelChange\"], [1, \"text-right\", \"pt-3\"], [\"nz-button\", \"\", 3, \"click\"], [1, \"w-[15vw]\"], [\"label\", \"Pole\"], [1, \"font-bold\", \"uppercase\"], [\"nzSize\", \"small\", 3, \"nzLoading\", \"nzData\"], [\"tableDocs\", \"\"], [3, \"ngClass\"], [1, \"text-sm\", \"text-yellow-600\"], [1, \"uppercase\"], [1, \"font-medium\", \"text-sm\", \"text-gray-400\"], [\"tablelisteVentes\", \"\"], [\"nz-checkbox\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"nz-checkbox\", \"\", 3, \"disabled\", \"ngModel\", \"nzCheckedChange\", \"ngModelChange\"], [3, \"nzExpand\", \"nzExpandChange\"], [3, \"nzColor\"], [\"nz-button\", \"\", \"nzType\", \"link\", \"nz-popconfirm\", \"\", \"nzPopconfirmPlacement\", \"bottom\", 3, \"nzPopconfirmTitle\", \"disabled\", \"nzLoading\", \"nzOnConfirm\"], [\"nz-icon\", \"\", \"nzType\", \"reload\", \"nzTheme\", \"outline\"], [3, \"nzExpand\"], [1, \"block\", \"w-full\", \"space-y-4\", \"overflow-x-auto\", \"rounded-lg\", \"border\", \"border-white-dark/20\", \"p-4\"], [1, \"bg-red-50\", \"flex\", \"min-w-[625px]\", \"items-center\", \"justify-around\", \"rounded-xl\", \"p-3\", \"font-semibold\", \"text-gray-500\", \"shadow-[0_0_4px_2px_rgb(31_45_61_/_10%)]\", \"transition-all\", \"duration-300\", \"hover:scale-[1.01]\"], [1, \"text-red-700\"], [1, \"flex\", \"min-w-[625px]\", \"items-center\", \"justify-between\", \"rounded-xl\", \"p-3\", \"font-semibold\", \"text-gray-500\", \"shadow-[0_0_4px_2px_rgb(31_45_61_/_10%)]\", \"transition-all\", \"duration-300\", \"hover:scale-[1.01]\", 3, \"ngClass\"], [1, \"text-green-700\"]],\n  template: function CompteFournisseurComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form-item\", 3)(4, \"nz-select\", 4);\n      i0.ɵɵlistener(\"ngModelChange\", function CompteFournisseurComponent_Template_nz_select_ngModelChange_4_listener($event) {\n        return ctx.filtre.frs = $event;\n      })(\"ngModelChange\", function CompteFournisseurComponent_Template_nz_select_ngModelChange_4_listener() {\n        return ctx.selectFournisseur();\n      });\n      i0.ɵɵtemplate(5, CompteFournisseurComponent_nz_option_5_Template, 1, 2, \"nz-option\", 5);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"div\", 6)(7, \"a\", 7)(8, \"span\");\n      i0.ɵɵtext(9, \"Imprimer\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(10, CompteFournisseurComponent_ng_template_10_Template, 10, 3, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 9)(13, \"a\", 10)(14, \"span\");\n      i0.ɵɵtext(15, \"Impression detaill\\u00E9\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(16, CompteFournisseurComponent_ng_template_16_Template, 12, 4, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(18, \"div\")(19, \"a\", 12);\n      i0.ɵɵlistener(\"click\", function CompteFournisseurComponent_Template_a_click_19_listener() {\n        return ctx.openForm({\n          cle: \"form_paiement\",\n          _ttl: \"Nouveau Paiement\",\n          _paiement: {\n            tpe_doc: \"regl_frs\",\n            sens: \"s\",\n            data_tiers: {\n              nom: ctx.getFrs(ctx.filtre.frs).nom_tiers,\n              tpe_tiers: ctx.filtre.frs.split(\"_\")[0]\n            },\n            tiers: ctx.getFrs(ctx.filtre.frs).id\n          },\n          _qp: {\n            tiers: ctx.filtre.frs\n          }\n        });\n      });\n      i0.ɵɵelementStart(20, \"div\", 13);\n      i0.ɵɵelement(21, \"span\", 14);\n      i0.ɵɵelementStart(22, \"span\");\n      i0.ɵɵtext(23, \"Cr\\u00E9er Paiement\");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(24, \"div\")(25, \"nz-tabset\")(26, \"nz-tab\", 15);\n      i0.ɵɵtemplate(27, CompteFournisseurComponent_ng_template_27_Template, 2, 0, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(29, \"div\", 17)(30, \"div\", 18);\n      i0.ɵɵelementContainerStart(31);\n      i0.ɵɵelementStart(32, \"div\", 19)(33, \"div\", 20)(34, \"div\", 21);\n      i0.ɵɵtemplate(35, CompteFournisseurComponent_nz_table_35_Template, 14, 3, \"nz-table\", 22);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(36, \"div\", 20)(37, \"nz-table\", 23, 24)(39, \"thead\")(40, \"tr\")(41, \"th\");\n      i0.ɵɵtext(42, \"Pi\\u00E8ces\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"th\");\n      i0.ɵɵtext(44, \"Journal\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"th\");\n      i0.ɵɵtext(46, \"Montant\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"th\");\n      i0.ɵɵtext(48, \"Debit\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"th\");\n      i0.ɵɵtext(50, \"Credit\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"th\");\n      i0.ɵɵtext(52, \"Etat\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(53, \"tbody\");\n      i0.ɵɵtemplate(54, CompteFournisseurComponent_tr_54_Template, 18, 15, \"tr\", 25);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(55, \"div\", 26)(56, \"form-item\", 27);\n      i0.ɵɵelement(57, \"input\", 28);\n      i0.ɵɵpipe(58, \"number\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(59, \"form-item\", 29);\n      i0.ɵɵelement(60, \"input\", 28);\n      i0.ɵɵpipe(61, \"number\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"form-item\", 30);\n      i0.ɵɵelement(63, \"input\", 28);\n      i0.ɵɵpipe(64, \"number\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementContainerEnd();\n      i0.ɵɵtemplate(65, CompteFournisseurComponent_ng_template_65_Template, 17, 5, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(67, \"nz-tab\", 15);\n      i0.ɵɵtemplate(68, CompteFournisseurComponent_ng_template_68_Template, 2, 0, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(70, \"div\", 18)(71, \"div\", 33)(72, \"div\")(73, \"nz-table\", 23, 34)(75, \"thead\")(76, \"tr\");\n      i0.ɵɵelement(77, \"th\", 35);\n      i0.ɵɵelementStart(78, \"th\");\n      i0.ɵɵtext(79, \"N\\u00B0\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(80, \"th\");\n      i0.ɵɵtext(81, \"Type\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(82, \"th\");\n      i0.ɵɵtext(83, \"Montant TTC\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(84, \"th\");\n      i0.ɵɵtext(85, \"M. Paiement\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(86, \"th\");\n      i0.ɵɵtext(87, \"Avoir\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(88, \"th\");\n      i0.ɵɵtext(89, \"Reste\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(90, \"th\");\n      i0.ɵɵtext(91, \"Reste reel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(92, \"th\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(93, \"tbody\");\n      i0.ɵɵtemplate(94, CompteFournisseurComponent_tr_94_Template, 24, 47, \"tr\", 36);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(95, \"div\", 37)(96, \"a\", 38);\n      i0.ɵɵlistener(\"click\", function CompteFournisseurComponent_Template_a_click_96_listener() {\n        return ctx.majBr({\n          avr: ctx.getDataList(\"achat.liste_selected_avr\", [\"tpe_doc\", \"==\", \"avrf\"]),\n          br: ctx.getDataList(\"achat.liste_selected_avr\", [\"tpe_doc\", \"==\", \"abr\"]),\n          tpe: \"frs\"\n        }, {\n          tiers: ctx.filtre.frs,\n          journal: [\"ve\", \"bq\", \"ca\", \"od\"],\n          avonce: false,\n          withdet: true\n        });\n      });\n      i0.ɵɵelementStart(97, \"div\", 13)(98, \"span\");\n      i0.ɵɵtext(99, \"Confirmer\");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(100, \"nz-table\", 23, 39)(102, \"thead\")(103, \"tr\");\n      i0.ɵɵelement(104, \"th\", 35);\n      i0.ɵɵelementStart(105, \"th\");\n      i0.ɵɵtext(106, \"Pi\\u00E8ces\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(107, \"th\");\n      i0.ɵɵtext(108, \"Type\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(109, \"th\");\n      i0.ɵɵtext(110, \"Journal\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(111, \"th\");\n      i0.ɵɵtext(112, \"Montant\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(113, \"th\");\n      i0.ɵɵtext(114, \"Debit\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(115, \"th\");\n      i0.ɵɵtext(116, \"Credit\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(117, \"th\");\n      i0.ɵɵtext(118, \"Reste\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(119, \"th\");\n      i0.ɵɵtext(120, \"Etat\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(121, \"tbody\");\n      i0.ɵɵtemplate(122, CompteFournisseurComponent_tr_122_Template, 26, 24, \"tr\", 25);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(123, \"div\", 37)(124, \"a\", 38);\n      i0.ɵɵlistener(\"click\", function CompteFournisseurComponent_Template_a_click_124_listener() {\n        return ctx.majAvonce({\n          bls: ctx.getDataList(\"achat.liste_selected_abr\"),\n          ecr: ctx.getDataList(\"compta.liste_ecritures\", [\"_chk\", \"==\", true]),\n          tpe: \"frs\"\n        }, ctx.filtre.frs);\n      });\n      i0.ɵɵelementStart(125, \"div\", 13)(126, \"span\");\n      i0.ɵɵtext(127, \"Confirmer\");\n      i0.ɵɵelementEnd()()()()()();\n      i0.ɵɵelementStart(128, \"nz-tab\", 15);\n      i0.ɵɵtemplate(129, CompteFournisseurComponent_ng_template_129_Template, 2, 0, \"ng-template\", null, 40, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(131, \"div\", 18)(132, \"nz-table\", 41, 42)(134, \"thead\")(135, \"tr\");\n      i0.ɵɵelement(136, \"th\", 43);\n      i0.ɵɵelementStart(137, \"th\");\n      i0.ɵɵtext(138, \"N\\u00B0\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(139, \"th\");\n      i0.ɵɵtext(140, \"Date\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(141, \"th\");\n      i0.ɵɵtext(142, \"Montant TTC\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(143, \"th\");\n      i0.ɵɵtext(144, \"Montant TTC\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(145, \"th\");\n      i0.ɵɵtext(146, \"Reste\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(147, \"th\");\n      i0.ɵɵtext(148, \"Situation paiment\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(149, \"th\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(150, \"tbody\");\n      i0.ɵɵtemplate(151, CompteFournisseurComponent_ng_container_151_Template, 28, 26, \"ng-container\", 44);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(152, \"div\", 26)(153, \"div\", 45)(154, \"a\", 10)(155, \"span\");\n      i0.ɵɵtext(156, \"Impression Historique de lettrage des reglements\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(157, CompteFournisseurComponent_ng_template_157_Template, 8, 2, \"ng-template\", null, 46, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd()()()()()()();\n      i0.ɵɵtemplate(159, CompteFournisseurComponent_comp_form_paiement_159_Template, 1, 0, \"comp-form-paiement\", 47);\n      i0.ɵɵelement(160, \"compte-fournisseur-print\")(161, \"compte-frs-print-let\")(162, \"compte-fournisseur-print-det\");\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(11);\n      const _r3 = i0.ɵɵreference(17);\n      const _r5 = i0.ɵɵreference(28);\n      const _r8 = i0.ɵɵreference(38);\n      const _r12 = i0.ɵɵreference(69);\n      const _r14 = i0.ɵɵreference(74);\n      const _r16 = i0.ɵɵreference(101);\n      const _r18 = i0.ɵɵreference(130);\n      const _r20 = i0.ɵɵreference(133);\n      const _r22 = i0.ɵɵreference(158);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngModel\", ctx.filtre.frs);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.getDataList(\"tiers.liste_frs\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", !ctx.filtre.frs)(\"nzPopoverContent\", _r1);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"disabled\", !ctx.filtre.frs)(\"nzPopoverContent\", _r3);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"disabled\", !ctx.achatCommercialPrivilege() || !ctx.filtre.frs);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"nzTitle\", _r5);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.getDataList(\"achat.liste_abr\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"nzData\", ctx.getDataList(\"compta.liste_ecritures\"))(\"nzLoading\", ctx.getDataString(\"abr.loading\") == \"1\");\n      i0.ɵɵadvance(17);\n      i0.ɵɵproperty(\"ngForOf\", _r8.data);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngClass\", \"bg-[#FF7B7B]\")(\"ngModel\", i0.ɵɵpipeBind2(58, 36, ctx.calculSoldeFrs(\"solde\", ctx.filtre.frs).debit, \".02-2\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngClass\", \"bg-[#A1D99B]\")(\"ngModel\", i0.ɵɵpipeBind2(61, 39, ctx.calculSoldeFrs(\"solde\", ctx.filtre.frs).credit, \".02-2\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngClass\", ctx.calculSoldeFrs(\"solde\", ctx.filtre.frs).solde >= 0 ? \"bg-[#A1D99B]\" : \"bg-[#FF7B7B] text-black\")(\"ngModel\", i0.ɵɵpipeBind2(64, 42, ctx.calculSoldeFrs(\"solde\", ctx.filtre.frs).solde, \".02-2\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"nzTitle\", _r12);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"nzData\", ctx.getBrAvonces())(\"nzLoading\", ctx.getDataString(\"abr.loading\") == \"1\");\n      i0.ɵɵadvance(21);\n      i0.ɵɵproperty(\"ngForOf\", _r14.data);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", !ctx.achatCommercialPrivilege() || !ctx.getDataList(\"achat.liste_abr\", i0.ɵɵpureFunction0(45, _c2)).length || ctx.getDataString(\"confirmabr.loading\") == \"1\")(\"nzLoading\", ctx.getDataString(\"confirmabr.loading\") == \"1\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"nzData\", ctx.getEcrituresAvonces())(\"nzLoading\", ctx.getDataString(\"abr.loading\") == \"1\");\n      i0.ɵɵadvance(22);\n      i0.ɵɵproperty(\"ngForOf\", _r16.data);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", !ctx.achatCommercialPrivilege() || !ctx.getDataList(\"achat.liste_abr\", i0.ɵɵpureFunction0(46, _c3)).length || !(ctx.getDataList(\"compta.liste_ecritures\", i0.ɵɵpureFunction0(47, _c3)).length == 1) || ctx.getDataString(\"confirmavc.loading\") == \"1\")(\"nzLoading\", ctx.getDataString(\"confirmavc.loading\") == \"1\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"nzTitle\", _r18);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"nzData\", ctx.getDataList(\"achat.liste_abr_history\"))(\"nzLoading\", ctx.getDataString(\"abrh.loading\") == \"1\");\n      i0.ɵɵadvance(19);\n      i0.ɵɵproperty(\"ngForOf\", _r20.data);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"disabled\", !ctx.filtre.frs)(\"nzPopoverContent\", _r22);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.isFormVisible(\"form_paiement\"));\n    }\n  },\n  dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.NzIconDirective, i6.NzInputDirective, i7.NzPopconfirmDirective, i8.NzCheckboxComponent, i9.NzDatePickerComponent, i10.NzOptionComponent, i10.NzSelectComponent, i11.NzPopoverDirective, i12.NzTableComponent, i12.NzTableCellDirective, i12.NzThMeasureDirective, i12.NzTdAddOnComponent, i12.NzTheadComponent, i12.NzTbodyComponent, i12.NzTrDirective, i12.NzTrExpandDirective, i12.NzTableFixedRowComponent, i13.NzTabSetComponent, i13.NzTabComponent, i14.NzTagComponent, i15.NzButtonComponent, i16.ɵNzTransitionPatchDirective, i17.NzWaveDirective, i18.FormItemComponent, i19.FormPaiementComponent, i20.CompteFournisseurPrintComponent, i21.CompteFrsPrintLetComponent, i22.CompteFournisseurPrintDetComponent, i3.DecimalPipe, i3.DatePipe, i23.LabByIdPipe],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "mappings": "AAGA,SAASA,eAAe,QAAQ,iCAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;ICGvDC,gCACwC;;;;IADsBA,2CAAyB;;;;;;IAUvFA,+BAAiC;IAEEA;MAAAA;MAAA;MAAA,OAAaA,kDAAyB;IAAA,EAAP;IAACA,iBAAiB;IAElFA,qCAA4B;IACKA;MAAAA;MAAA;MAAA,OAAaA,gDAAuB;IAAA,EAAP;IAACA,iBAAiB;IAGlFA,iCAAsD;IAAtBA;MAAAA;MAAA;MAAA;IAAA,EAAqB;IAACA,mCAAc;IAAAA,iBAAQ;IAC5EA,+BAA6B;IACTA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,wBAAQ;IAAAA,iBAAS;;;;IARvBA,eAA+B;IAA/BA,kDAA+B;IAG/BA,eAA6B;IAA7BA,gDAA6B;IAGhCA,eAAqB;IAArBA,wCAAqB;;;;;IAuB/CA,gCAAiG;;;;IAAvDA,uCAAqB;;;;;;IAXrEA,+BAAiC;IAEEA;MAAAA;MAAA;MAAA,OAAaA,kDAAyB;IAAA,EAAP;IAACA,iBAAiB;IAElFA,qCAA4B;IACKA;MAAAA;MAAA;MAAA,OAAaA,gDAAuB;IAAA,EAAP;IAACA,iBAAiB;IAGlFA,+BAAsB;IAEkCA;MAAAA;MAAA;MAAA,OAAaA,4CAC1E;IAAA,EADsF;IAC3EA,sGAAiG;IACnGA,iBAAY;IAGhBA,+BAA6B;IACTA;MAAAA;MAAA;MAAA,OAASA,mCAAY,qBAAqB,CAAC;IAAA,EAAC;IAACA,yBAAQ;IAAAA,iBAAS;;;;IAd/CA,eAA+B;IAA/BA,kDAA+B;IAG/BA,eAA6B;IAA7BA,gDAA6B;IAKRA,eAAyB;IAAzBA,4CAAyB;IAChDA,eAAa;IAAbA,2CAAa;;;;;IAwC9CA,+BAAiC;IAAAA,kCAAkB;IAAAA,iBAAM;;;;;;;;;;IAmB3CA,8BAAgF;IAEvEA,YAAwB;IAAAA,iBAAM;IACnCA,gCAAsC;IAAAA,YAAsB;IAAAA,iBAAO;IAErEA,0BAAI;IACFA,YACF;IAAAA,iBAAK;IACLA,0BAAI;IACIA,aAAwC;;IAAAA,iBAAO;IAEvDA,2BAAI;IAAAA,aACO;;IAAAA,iBAAK;;;;IAZqBA,gGAAwC;IAEtEA,eAAwB;IAAxBA,4CAAwB;IACSA,eAAsB;IAAtBA,4CAAsB;IAG5DA,eACF;IADEA,gDACF;IAEQA,eAAwC;IAAxCA,2EAAwC;IAE5CA,eACO;IADPA,6IACO;;;;;IAvBjBA,wCACyE;IAG/DA,uBAAE;IAAAA,iBAAK;IACXA,0BAAI;IAAAA,oBAAI;IAAAA,iBAAK;IACbA,0BAAI;IAAAA,oBAAI;IAAAA,iBAAK;IACbA,2BAAI;IAAAA,4BAAW;IAAAA,iBAAK;IAGxBA,8BAAO;IACLA,0FAaK;IACPA,iBAAQ;;;;;;IAxBRA,sEAAiD;IAU3BA,gBAAiB;IAAjBA,mCAAiB;;;;;IAkDnCA,4BAAkC;IAAAA,YAE9B;;IAAAA,iBAAO;;;;IAFuBA,eAE9B;IAF8BA,uEAE9B;;;;;IAGJA,4BAAmC;IAAAA,YAE/B;;IAAAA,iBAAO;;;;IAFwBA,eAE/B;IAF+BA,wEAE/B;;;;;IAtBRA,0BAC+G;IAIzGA,YACF;IAAAA,iBAAM;IACNA,+BAA+C;IAC7CA,YACF;;IAAAA,iBAAM;IAGRA,8BAAsB;IAAAA,YAAiB;IAAAA,iBAAK;IAC5CA,0BAAI;IAAAA,aAA6D;;IAAAA,iBAAK;IACtEA,2BAAI;IACFA,sFAEW;IACbA,iBAAK;IACLA,2BAAI;IACFA,sFAEW;IACbA,iBAAK;IACLA,+BAAsB;IACpBA,aACF;IAAAA,iBAAK;;;;IAzBLA,6PAA4G;IAIxGA,eACF;IADEA,0EACF;IAEEA,eACF;IADEA,qIACF;IAGoBA,eAAiB;IAAjBA,qCAAiB;IACnCA,eAA6D;IAA7DA,0IAA6D;IAExDA,eAAyB;IAAzBA,8CAAyB;IAKzBA,eAA0B;IAA1BA,+CAA0B;IAKjCA,eACF;IADEA,2UACF;;;;;IAmDFA,4BAAkC;IAAAA,YAE9B;;IAAAA,iBAAO;;;;IAFuBA,eAE9B;IAF8BA,uEAE9B;;;;;IAGJA,4BAAmC;IAAAA,YAE/B;;IAAAA,iBAAO;;;;IAFwBA,eAE/B;IAF+BA,wEAE/B;;;;;;IAnBRA,0BAA8C;IACrBA;MAAA;MAAA;MAAA,OAAaA,qCAAgB;IAAA,EAAP;IAACA,iBAAQ;IACtDA,0BAAI;IAEAA,YACF;IAAAA,iBAAM;IACNA,YACF;;IAAAA,iBAAK;IACLA,8BAAsB;IAAAA,YAAiB;IAAAA,iBAAK;IAC5CA,+BAAsB;IAAAA,aAAiB;IAAAA,iBAAK;IAE5CA,2BAAI;IACFA,qGAEW;IACbA,iBAAK;IACLA,2BAAI;IACFA,qGAEW;IACbA,iBAAK;;;;IAnBkBA,eAAsB;IAAtBA,sCAAsB;IAGzCA,eACF;IADEA,0EACF;IACAA,eACF;IADEA,qFACF;IACsBA,eAAiB;IAAjBA,qCAAiB;IACjBA,eAAiB;IAAjBA,qCAAiB;IAG9BA,eAAyB;IAAzBA,8CAAyB;IAKzBA,eAA0B;IAA1BA,+CAA0B;;;;;;;;IAhCzCA,wCAEoD;IAG9CA,yBAAuB;IACvBA,0BAAI;IAAAA,2BAAM;IAAAA,iBAAK;IACfA,0BAAI;IAAAA,oBAAI;IAAAA,iBAAK;IACbA,0BAAI;IAAAA,wBAAO;IAAAA,iBAAK;IAEhBA,2BAAI;IAAAA,sBAAK;IAAAA,iBAAK;IACdA,2BAAI;IAAAA,uBAAM;IAAAA,iBAAK;IAGnBA,8BAAO;IACLA,6FAqBK;IACPA,iBAAQ;;;;;IArCiCA,iJACqD;IAcxEA,gBAAwB;IAAxBA,mCAAwB;;;;;IA8BpDA,+BAAiC;IAAAA,uBAAO;IAAAA,iBAAM;;;;;;IAqBtCA,8BAA2F;IAErEA;MAAA;MAAA;MAAA;MAAA,OAAmBA,0CAAe;IAAA,EAAC;MAAA;MAAA;MAAA,OACtCA,qCAAgB;IAAA,EADsB;IAC5BA,iBAAO;IAElCA,8BACoB;IAAAA,YAAwB;IAAAA,iBAAK;IACjDA,8BACoB;IAAAA,YAAiB;IAAAA,iBAAK;IAC1CA,0BAAiG;IAAAA,YAEtF;;IAAAA,iBAAK;IAChBA,2BAAiG;IAAAA,aAEtF;;IAAAA,iBAAK;IAChBA,2BAEG;IAAAA,aAAwE;;IAAAA,iBAAK;IAChFA,2BAAiG;IAAAA,aAEjG;;IAAAA,iBAAK;IAELA,2BAAiG;IAAAA,aACV;;IAAAA,iBAAK;IAC5FA,2BAAiG;IAC7EA;MAAA;MAAA;MAAA;MAAA,OAAmBA,6CAAkB;IAAA,EAAC;MAAA;MAAA;MAAA,OAEzCA,sCAAiB;IAAA,EAFwB;IAE9BA,iBAAO;;;;;IA3BaA,gGAAwC;IACpFA,eAA4F;IAA5FA,kMAA4F;IACxCA,eAA8B;IAA9BA,4EAA8B;IAGlFA,eAA4F;IAA5FA,kMAA4F;IAC5EA,eAAwB;IAAxBA,4CAAwB;IACxCA,eAA4F;IAA5FA,kMAA4F;IAC5EA,eAAiB;IAAjBA,qCAAiB;IACjCA,eAA4F;IAA5FA,kMAA4F;IAACA,eAEtF;IAFsFA,6HAEtF;IACPA,eAA4F;IAA5FA,kMAA4F;IAACA,eAEtF;IAFsFA,4FAEtF;IAETA,eAAkI;IAAlIA,wOAAkI;IACjIA,eAAwE;IAAxEA,oIAAwE;IACvEA,eAA4F;IAA5FA,kMAA4F;IAACA,eAEjG;IAFiGA,2FAEjG;IAEIA,eAA4F;IAA5FA,kMAA4F;IAACA,eACV;IADUA,gJACV;IACnFA,eAA4F;IAA5FA,kMAA4F;IAE5FA,eAA4E;IAA5EA,wGAA4E;;;;;IA2C9EA,4BAAgC;IAAAA,YAAuC;;IAAAA,iBAAO;;;;IAA9CA,eAAuC;IAAvCA,uEAAuC;;;;;IACvEA,4BAAiC;IAAAA,YAAwC;;IAAAA,iBAAO;;;;IAA/CA,eAAwC;IAAxCA,wEAAwC;;;;;IAG3EA,4BAAmC;IACjCA,YAEF;IAAAA,iBAAO;;;;IAFLA,eAEF;IAFEA,wRAEF;;;;;;IAjBJA,0BAC+G;IACtFA;MAAA;MAAA;MAAA,OAAaA,qCAAgB;IAAA,EAAP;IAACA,iBAAQ;IACtDA,0BAAI;IACqBA,YAAuC;IAAAA,iBAAM;IACpEA,YACF;;;IAAAA,iBAAK;IACLA,8BAAsB;IAAAA,aAAiB;IAAAA,iBAAK;IAC5CA,+BAAsB;IAAAA,aAAiB;IAAAA,iBAAK;IAC5CA,2BAAI;IAAAA,aAA6D;;IAAAA,iBAAK;IACtEA,2BAAI;IAAAA,uFAA8E;IAAAA,iBAAK;IACvFA,2BAAI;IAAAA,uFAAgF;IAAAA,iBAAK;IACzFA,2BAAI;IAAMA,aAAoD;;IAAAA,iBAAO;IACrEA,+BAAsB;IACpBA,uFAGO;IACTA,iBAAK;;;;IAjBLA,6PAA4G;IACrFA,eAAsB;IAAtBA,sCAAsB;IAEpBA,eAAuC;IAAvCA,+DAAuC;IAC9DA,eACF;IADEA,2KACF;IACsBA,eAAiB;IAAjBA,qCAAiB;IACjBA,eAAiB;IAAjBA,qCAAiB;IACnCA,eAA6D;IAA7DA,0IAA6D;IACtDA,eAAuB;IAAvBA,8CAAuB;IACvBA,eAAwB;IAAxBA,+CAAwB;IACzBA,eAAoD;IAApDA,sFAAoD;IAErDA,eAA0B;IAA1BA,yGAA0B;;;;;IAuB3CA,+BAAiC;IAAAA,qDAAqC;IAAAA,iBAAM;;;;;IA0ChEA,6BAAkD;IAChDA,+BACuM;IAC/LA,uBAAM;IAAAA,iBAAM;IAClBA,+BAA0B;IAACA,YAAwC;;IAAAA,iBAAM;IAE7EA,0BAAe;;;;IAFgBA,eAAwC;IAAxCA,mFAAwC;;;;;IALzEA,6BAA+C;IAC7CA,+HAMe;IACjBA,0BAAe;;;;IAPiBA,eAAkB;IAAlBA,6CAAkB;;;;;IAQlDA,6BAAiD;IAC/CA,+BAC8L;IAE1LA,YACF;;IAAAA,iBAAM;IACNA,2BAAK;IAACA,YAAwC;IAAAA,iBAAM;IACpDA,+BAA4B;IAACA,YAI7B;;IAAAA,iBAAM;IACNA,4BAAK;IAACA,aAA2C;;IAAAA,iBAAM;IACvDA,4BAAK;IAACA,aAAgF;;IAAAA,iBAAM;IAEhGA,0BAAe;;;;;;IAdRA,eAAiE;IAAjEA,uFAAiE;IAGlEA,eACF;IADEA,qFACF;IACMA,eAAwC;IAAxCA,2EAAwC;IACjBA,eAI7B;IAJ6BA,4UAI7B;IACMA,eAA2C;IAA3CA,uFAA2C;IAC3CA,eAAgF;IAAhFA,6HAAgF;;;;;;IA7ChGA,6BAAgD;IAC9CA,0BAAI;IACsCA;MAAA;MAAA;MAAA;MAAA,OAAkBA,0DAA+B;IAAA,EAAC;IAACA,iBAAK;IAChGA,0BAAI;IAAAA,YAAyB;IAAAA,iBAAK;IAClCA,0BAAI;IAAAA,YAAyC;;IAAAA,iBAAK;IAClDA,0BAAI;IAAAA,YAAqD;;IAAAA,iBAAK;IAC9DA,2BAAI;IAAAA,aAA2D;;IAAAA,iBAAK;IACpEA,2BAAI;IAAAA,aAAgD;;IAAAA,iBAAK;IACzDA,+BAAsB;IAE+FA,aAA6B;IAAAA,iBAAS;IAE3JA,+BAAsB;IAIlBA;MAAA;MAAA;MAAA;MAAA,OAAeA,uCAAgB,IAAIC,8CAA0B;IAAA,EAAC;IAC9DD,4BAAuD;IACzDA,iBAAI;IAGRA,+BAAwC;IAEpCA,iHAQe;IACfA,mHAee;IACjBA,iBAAM;IAERA,sBAAS;IACXA,0BAAe;;;;;IAjDPA,eAAmC;IAAnCA,6DAAmC;IACnCA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAAyC;IAAzCA,4EAAyC;IACzCA,eAAqD;IAArDA,uFAAqD;IACrDA,eAA2D;IAA3DA,6FAA2D;IAC3DA,eAAgD;IAAhDA,kFAAgD;IAGhDA,eAAgH;IAAhHA,mJAAgH;IAACA,eAA6B;IAA7BA,mDAA6B;IAGvGA,eAAgD;IAAhDA,sEAAgD;IAQzFA,eAAmC;IAAnCA,6DAAmC;IAEpBA,eAA8B;IAA9BA,qDAA8B;IASfA,eAAiB;IAAjBA,4CAAiB;;;;;;IA6BnDA,+BAAiC;IAEEA;MAAAA;MAAA;MAAA,OAAaA,kDAAyB;IAAA,EAAP;IAACA,iBAAiB;IAElFA,qCAA4B;IACKA;MAAAA;MAAA;MAAA,OAAaA,gDAAuB;IAAA,EAAP;IAACA,iBAAiB;IAGlFA,+BAA6B;IACTA;MAAAA;MAAA;MAAA,OAASA,mCAAY,qBAAqB,CAAC;IAAA,EAAC;IAACA,wBAAQ;IAAAA,iBAAS;;;;IAP/CA,eAA+B;IAA/BA,mDAA+B;IAG/BA,eAA6B;IAA7BA,iDAA6B;;;;;IAchFA,qCAAgF;;;;;;;;;ADxbhF,OAAM,MAAOE,0BAA2B,SAAQH,eAAe;EAG7DI,cAAc,CAACC,EAAU,EAAEC,OAAgB;IACzC,IAAIA,OAAO,EAAE;MACX,IAAI,CAACC,SAAS,CAACC,GAAG,CAACH,EAAE,CAAC;KACvB,MAAM;MACL,IAAI,CAACE,SAAS,CAACE,MAAM,CAACJ,EAAE,CAAC;;EAE7B;EAWAK,YACqBC,GAAe,EACfC,cAA8B,EACzCC,QAAkB;IAE1B,KAAK,CAACF,GAAG,EAAEC,cAAc,CAAC;IAJP,QAAG,GAAHD,GAAG;IACH,mBAAc,GAAdC,cAAc;IACzB,aAAQ,GAARC,QAAQ;IAtBlB,cAAS,GAAG,IAAIC,GAAG,EAAU;IAC7B,YAAO,GAAG,IAAI;IAQd,eAAU,GAAG,CACX;MACEC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACED,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;KACR,CACF;EAOD;EACAC,QAAQ;IACN,IAAI,CAACC,QAAQ,CAAC,kCAAkC,CAAC;EACnD;EACAC,cAAc,CAACC,IAAS,EAAEC,YAAiB;IACzC,IAAID,IAAI,EAAE;MACR,MAAME,OAAO,GAAGF,IAAI,CAACG,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACnB,EAAE,IAAIgB,YAAY,CAAChB,EAAE,CAAC;MAE9D,IAAIiB,OAAO,EACT,OAAOA,OAAO,CAACA,OAAO,IAAI,CAAC;;IAG/B,OAAO,CAAC;EACV;EACAG,iBAAiB;IACf,IAAI,CAACC,aAAa,CAAC,aAAa,EAAE,GAAG,CAAC;IACtC,IAAI,CAACA,aAAa,CAAC,cAAc,EAAE,GAAG,CAAC;IACvC,IAAI,CAACC,WAAW,CAAC,0BAA0B,EAAE,EAAE,CAAC;IAChD,IAAI,IAAI,CAACC,MAAM,CAACC,GAAG,IAAIC,SAAS,EAAE;MAChC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACH,MAAM,CAACC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACxE,IAAI,CAACG,mBAAmB,CAAC,IAAI,CAACJ,MAAM,CAACC,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;;EAGlE;EAEAC,mBAAmB;IACjB,OAAO,IAAI,CAACC,WAAW,CAAC,wBAAwB,CAAC,CAACC,MAAM,CAAEC,QAAa,IAAMA,QAAQ,CAACC,OAAO,KAAK,UAAU,IAAKD,QAAQ,CAACE,kBAAkB,CAACC,KAAK,GAAG,CAAC,IAAIH,QAAQ,CAACI,aAAa,IAAI,IAAI,IAAIJ,QAAQ,CAACK,UAAU,CAACC,SAAS,IAAI,IAAI,CAACf,MAAM,EAAEC,GAAG,EAAEI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;EACrQ;EAEAW,YAAY;IACV,OAAO,IAAI,CAACT,WAAW,CAAC,iBAAiB,CAAC,CAACC,MAAM,CAAES,EAAO,IAAK,CAACA,EAAE,CAACJ,aAAa,CAACK,MAAM,IAAI,QAAQ,IAAID,EAAE,CAACJ,aAAa,CAACK,MAAM,IAAI,UAAU,KAAKD,EAAE,CAACE,SAAS,IAAI,CAAC,CAAC;EACrK;EAEAP,KAAK;IACH,OAAO,IAAI,CAACL,WAAW,CAAC,iBAAiB,CAAC,CAACC,MAAM,CAACY,GAAG,IAAIA,GAAG,CAACC,IAAI,IAAI,IAAI,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEH,GAAG,KAAKG,GAAG,GAAGH,GAAG,CAACP,aAAa,CAACD,KAAK,EAAE,CAAC,CAAC;EACnI;EAEAlB,OAAO;IACL,OAAO,IAAI,CAACY,mBAAmB,EAAE,CAACE,MAAM,CAACY,GAAG,IAAIA,GAAG,CAACC,IAAI,IAAI,IAAI,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEH,GAAG,KAAKG,GAAG,GAAGH,GAAG,CAACI,KAAK,EAAE,CAAC,CAAC;EAC5G;EAEAC,MAAM,CAACC,OAAY;IACjB,OAAO,IAAI,CAACnB,WAAW,CACrB,eAAemB,OAAO,CAACrB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CACvC,CAACV,IAAI,CAAEgC,EAAE,IAAKA,EAAE,CAAClD,EAAE,IAAIiD,OAAO,CAACrB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;EAEAuB,UAAU,CAACC,EAAO;IAChB,IAAIA,EAAE,CAACR,IAAI,EAAE;MACX,IAAI,CAACtB,WAAW,CAAC,0BAA0B,EAAE,IAAI,CAACQ,WAAW,CAAC,0BAA0B,CAAC,CAACuB,MAAM,CAACD,EAAE,CAAC,CAAC;KACtG,MACI;MACH,IAAI,CAACtB,WAAW,CAAC,0BAA0B,CAAC,CAACwB,MAAM,CAAC,IAAI,CAACxB,WAAW,CAAC,0BAA0B,CAAC,CAACyB,OAAO,CAACH,EAAE,CAAC,EAAE,CAAC,CAAC;;EAEpH;EAEAI,aAAa,CAACJ,EAAO;IACnB,IAAIA,EAAE,CAACK,KAAK,EAAE;MACZ,IAAI,CAACnC,WAAW,CAAC,0BAA0B,EAAE,IAAI,CAACQ,WAAW,CAAC,0BAA0B,CAAC,CAACuB,MAAM,CAACD,EAAE,CAAC,CAAC;KACtG,MACI;MACH,IAAI,CAACtB,WAAW,CAAC,0BAA0B,CAAC,CAACwB,MAAM,CAAC,IAAI,CAACxB,WAAW,CAAC,0BAA0B,CAAC,CAACyB,OAAO,CAACH,EAAE,CAAC,EAAE,CAAC,CAAC;;EAEpH;EAEAM,QAAQ;IACN,IAAI,CAACnC,MAAM,CAACoC,UAAU,GAAG,IAAI,CAACnD,QAAQ,CAACoD,SAAS,CAAC,IAAI,CAACrC,MAAM,CAACoC,UAAU,EAAE,YAAY,CAAC;IACtF,IAAI,CAACpC,MAAM,CAACsC,QAAQ,GAAG,IAAI,CAACrD,QAAQ,CAACoD,SAAS,CAAC,IAAI,CAACrC,MAAM,CAACsC,QAAQ,EAAE,YAAY,CAAC;IAClF,MAAMrC,GAAG,GAAG,IAAI,CAACM,WAAW,CAC1B,iBAAiB,CAClB,CAACZ,IAAI,CAAE4C,CAAC,IAAKA,CAAC,CAAC9D,EAAE,IAAI,IAAI,CAAC+D,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CAAC;IAC3D,IAAIwC,SAAS,GAAG,IAAI,CAAClC,WAAW,CAAC,wBAAwB,EAAE,CACzD,OAAO,EACP,IAAI,EACJ,IAAI,CAACiC,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CACrC,CAAC;IACFwC,SAAS,GAAGA,SAAS,CAACjC,MAAM,CAACkC,GAAG,IAAK,IAAIC,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACoC,UAAU,CAAC,IAAM,IAAIO,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACsC,QAAQ,CAAE,CAAC;IAC/J,IAAIO,GAAG,GAAG,IAAI,CAACtC,WAAW,CAAC,iBAAiB,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAACiC,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC;IACpG4C,GAAG,GAAG,IAAI,CAACtC,WAAW,CAAC,iBAAiB,CAAC,CAACC,MAAM,CAACsC,EAAE,IAAK,IAAIH,IAAI,CAACG,EAAE,CAACF,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACoC,UAAU,CAAC,IAAM,IAAIO,IAAI,CAACG,EAAE,CAACF,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACsC,QAAQ,CAAE,CAAC;IAChL,IAAI,IAAI,CAAC5D,OAAO,EAAE;MAChB+D,SAAS,GAAGA,SAAS,CAACX,MAAM,CAACe,GAAG,CAAC;KAClC,MACI;MACHJ,SAAS,GAAGI,GAAG;;IAEjB,IAAI,CAACE,YAAY,CAAC;MAChB5D,IAAI,EAAE,yBAAyB;MAC/BK,IAAI,EAAE;QACJS,GAAG;QACHwC,SAAS;QACTI,GAAG;QACHnE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB0D,UAAU,EAAE,IAAI,CAACpC,MAAM,CAACoC,UAAU;QAClCE,QAAQ,EAAE,IAAI,CAACtC,MAAM,CAACsC,QAAQ;QAC9Bd,KAAK,EAAE,IAAI,CAACwB,mBAAmB,EAAE,CAACxB,KAAK;QACvCyB,KAAK,EAAE,IAAI,CAACD,mBAAmB,EAAE,CAACC,KAAK;QACvCC,MAAM,EAAE,IAAI,CAACF,mBAAmB,EAAE,CAACE;;KAEtC,CAAC;EACJ;EAEAF,mBAAmB;IACjB,IAAIxB,KAAK,GAAG,CAAC;IACb,IAAIyB,KAAK,GAAG,CAAC;IACb,IAAIC,MAAM,GAAG,CAAC;IACd,MAAMjD,GAAG,GAAG,IAAI,CAACM,WAAW,CAC1B,iBAAiB,CAClB,CAACZ,IAAI,CAAE4C,CAAC,IAAKA,CAAC,CAAC9D,EAAE,IAAI,IAAI,CAAC+D,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CAAC;IAC3D,IAAIwC,SAAS,GAAG,IAAI,CAAClC,WAAW,CAAC,wBAAwB,EAAE,CACzD,OAAO,EACP,IAAI,EACJ,IAAI,CAACiC,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CACrC,CAAC;IACFwC,SAAS,GAAGA,SAAS,CAACjC,MAAM,CAACkC,GAAG,IAAK,IAAIC,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACoC,UAAU,CAAC,IAAM,IAAIO,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACsC,QAAQ,CAAE,CAAC;IAC/J,IAAIO,GAAG,GAAG,IAAI,CAACtC,WAAW,CAAC,iBAAiB,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAACiC,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC;IACpG4C,GAAG,GAAG,IAAI,CAACtC,WAAW,CAAC,iBAAiB,CAAC,CAACC,MAAM,CAACsC,EAAE,IAAK,IAAIH,IAAI,CAACG,EAAE,CAACF,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACoC,UAAU,CAAC,IAAM,IAAIO,IAAI,CAACG,EAAE,CAACF,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACsC,QAAQ,CAAE,CAAC;IAChL,IAAIG,SAAS,EAAE;MACbA,SAAS,CAACU,OAAO,CAAE1C,QAAa,IAAI;QAClCwC,KAAK,IAAIxC,QAAQ,CAAC2C,WAAW;QAC7BF,MAAM,IAAIzC,QAAQ,CAAC4C,YAAY;MACjC,CAAC,CAAC;;IAEJR,GAAG,CAACM,OAAO,CAAEN,GAAQ,IAAI;MACvB,IAAI,CAACA,GAAG,CAACS,QAAQ,EAAE;QACjBJ,MAAM,IAAIL,GAAG,CAACU,YAAY,CAACC,MAAM,IAAI,CAAC;OACvC,MACI;QACHP,KAAK,IAAIJ,GAAG,CAACU,YAAY,CAACC,MAAM,IAAI,CAAC;;IAEzC,CAAC,CAAC;IACF,OAAO;MAAEP,KAAK;MAAEC,MAAM;MAAE1B,KAAK,EAAEyB,KAAK,GAAGC;IAAM,CAAE;EACjD;EAEAO,WAAW,CAACtE,IAAS;IACnB,IAAI,CAACa,MAAM,CAACoC,UAAU,GAAG,IAAI,CAACnD,QAAQ,CAACoD,SAAS,CAAC,IAAI,CAACrC,MAAM,CAACoC,UAAU,EAAE,YAAY,CAAC;IACtF,IAAI,CAACpC,MAAM,CAACsC,QAAQ,GAAG,IAAI,CAACrD,QAAQ,CAACoD,SAAS,CAAC,IAAI,CAACrC,MAAM,CAACsC,QAAQ,EAAE,YAAY,CAAC;IAClF,MAAMrC,GAAG,GAAG,IAAI,CAACM,WAAW,CAC1B,iBAAiB,CAClB,CAACZ,IAAI,CAAE4C,CAAC,IAAKA,CAAC,CAAC9D,EAAE,IAAI,IAAI,CAAC+D,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CAAC;IAC3D,IAAIA,GAAG,EAAE;MACP,IAAIyD,IAAI,GAAG,IAAI,CAACnD,WAAW,CAAC,yBAAyB,CAAC,CAACC,MAAM,CAACkC,GAAG,IAAK,IAAIC,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACoC,UAAU,CAAC,IAAM,IAAIO,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACsC,QAAQ,CAAE,CAAC;MAChM,IAAI,CAACS,YAAY,CAAC;QAChB5D,IAAI;QACJK,IAAI,EAAE;UACJiD,SAAS,EAAEiB,IAAI;UACfzD,GAAG;UACHmC,UAAU,EAAE,IAAI,CAACpC,MAAM,CAACoC,UAAU;UAClCE,QAAQ,EAAE,IAAI,CAACtC,MAAM,CAACsC;;OAEzB,CAAC;;EAEN;EACAqB,WAAW,CAACxE,IAAS;IACnB,IAAI,CAACa,MAAM,CAACoC,UAAU,GAAG,IAAI,CAACnD,QAAQ,CAACoD,SAAS,CAAC,IAAI,CAACrC,MAAM,CAACoC,UAAU,EAAE,YAAY,CAAC;IACtF,IAAI,CAACpC,MAAM,CAACsC,QAAQ,GAAG,IAAI,CAACrD,QAAQ,CAACoD,SAAS,CAAC,IAAI,CAACrC,MAAM,CAACsC,QAAQ,EAAE,YAAY,CAAC;IAClF,MAAMrC,GAAG,GAAG,IAAI,CAACM,WAAW,CAC1B,iBAAiB,CAClB,CAACZ,IAAI,CAAE4C,CAAC,IAAKA,CAAC,CAAC9D,EAAE,IAAI,IAAI,CAAC+D,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CAAC;IAC3D,IAAIA,GAAG,EAAE;MACP,IAAI4C,GAAG,GAAG,IAAI,CAACtC,WAAW,CAAC,iBAAiB,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAACiC,cAAc,CAAC,IAAI,CAACxC,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC,CAACO,MAAM,CAACkC,GAAG,IAAK,IAAIC,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACoC,UAAU,CAAC,IAAM,IAAIO,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAI,IAAID,IAAI,CAAC,IAAI,CAAC3C,MAAM,CAACsC,QAAQ,CAAE,CAAC;MAC9O,IAAI,CAACS,YAAY,CAAC;QAChB5D,IAAI;QACJK,IAAI,EAAE;UACJiD,SAAS,EAAEI,GAAG;UACd5C,GAAG;UACHmC,UAAU,EAAE,IAAI,CAACpC,MAAM,CAACoC,UAAU;UAClCE,QAAQ,EAAE,IAAI,CAACtC,MAAM,CAACsC,QAAQ;UAC9BsB,IAAI,EAAE,IAAI,CAAC5D,MAAM,CAAC4D,IAAI,IAAI;;OAE7B,CAAC;;EAEN;EAEAC,eAAe,CAACC,GAAQ;IACtB,IAAIpE,OAAO,GAAG,CAAC;IACf,KAAK,IAAIqE,CAAC,IAAID,GAAG,CAACE,UAAU,EAAE;MAC5BtE,OAAO,IAAIqE,CAAC,CAACE,EAAE,IAAI,CAAC;;IAEtB,OAAOvE,OAAO;EAChB;;AA7MWnB,0BAA2B;mBAA3BA,0BAA0B;AAAA;AAA1BA,0BAA2B;QAA3BA,0BAA0B;EAAA2F;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCXvClG,8BAAgC;MAI4BA;QAAA;MAAA,EAAwB;QAAA,OACzDmG,uBAAmB;MAAA,EADsC;MAE1EnG,uFACwC;MAC1CA,iBAAY;MAEdA,8BAA6B;MAGnBA,wBAAQ;MAAAA,iBAAO;MAEvBA,+HAac;MAChBA,iBAAM;MACNA,+BAA2B;MAGjBA,yCAAmB;MAAAA,iBAAO;MAElCA,gIAmBc;MAChBA,iBAAM;MAERA,4BAAK;MAE6BA;QAAA,OAEtCmG;UAAAC,KAA4B,eACnB;UAAAC,MAAoB,kBACpB;UAAAC;YAAAjE,SACyB,UACzB;YAAAkE,MACH,GAAG;YAAA9D;cAAA+D,KACuBL,0BAAkB;cAAAzD,WACZyD,qBACnC,GAAG,CAAC,CAAC,CAAC;YAAA;YAAAM,OACuBN,0BACzB;UAAA;UAAAO;YAAAD;UAAA;QAAA,EAGG;MAAA,EAAD;MACDzG,gCAAqC;MACnCA,4BAAqD;MACrDA,6BAAM;MAAAA,oCAAc;MAAAA,iBAAO;MAMnCA,4BAAK;MAGCA,+HAEc;MACdA,gCAAyB;MAErBA,8BAAc;MACZA,gCAAwB;MAGlBA,yFA0BW;MACbA,iBAAM;MAERA,gCAAqB;MAOTA,4BAAM;MAAAA,iBAAK;MAEfA,2BAAI;MAAAA,wBAAO;MAAAA,iBAAK;MAChBA,2BAAI;MAAAA,wBAAO;MAAAA,iBAAK;MAChBA,2BAAI;MAAAA,sBAAK;MAAAA,iBAAK;MACdA,2BAAI;MAAAA,uBAAM;MAAAA,iBAAK;MACfA,2BAAI;MAAAA,qBAAI;MAAAA,iBAAK;MAGjBA,8BAAO;MACLA,8EA2BK;MACPA,iBAAQ;MAIdA,gCAA2D;MAEvDA,6BAC4E;;MAC9EA,iBAAY;MACZA,sCAA0B;MACxBA,6BAC6E;;MAC/EA,iBAAY;MACZA,sCAAyB;MACvBA,6BAG4E;;MAC9EA,iBAAY;MAEhBA,0BAAe;MACfA,gIAwCc;MAChBA,iBAAM;MAGVA,mCAA6C;MAC3CA,+HAEc;MACdA,gCAA0B;MAOdA,0BAAuB;MACvBA,2BAAI;MAAAA,wBAAE;MAAAA,iBAAK;MACXA,2BAAI;MAAAA,qBAAI;MAAAA,iBAAK;MACbA,2BAAI;MAAAA,4BAAW;MAAAA,iBAAK;MACpBA,2BAAI;MAAAA,4BAAW;MAAAA,iBAAK;MACpBA,2BAAI;MAAAA,sBAAK;MAAAA,iBAAK;MACdA,2BAAI;MAAAA,sBAAK;MAAAA,iBAAK;MACdA,2BAAI;MAAAA,2BAAU;MAAAA,iBAAK;MACnBA,sBAAS;MACXA,iBAAK;MAEPA,8BAAO;MACLA,8EA6BK;MACPA,iBAAQ;MAEVA,gCAA8B;MAG1BA;QAAA,OAASmG;UAAAQ,KAAYR,gBAAY,0BAA0B,GAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE;UAAAvD,IAAMuD,gBAAY,0BAA0B,GAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE;UAAAS,KAAO;QAAK;UAAAH;UAAAI,UAAsC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;UAAAC,QAAU,KAAK;UAAAC,SAAW;QAAI,EAAE;MAAA,EAAE;MAEtQ/G,gCAAqC;MAC7BA,0BAAS;MAAAA,iBAAO;MAK9BA,0CACoD;MAG9CA,2BAAuB;MACvBA,4BAAI;MAAAA,6BAAM;MAAAA,iBAAK;MACfA,4BAAI;MAAAA,sBAAI;MAAAA,iBAAK;MACbA,4BAAI;MAAAA,yBAAO;MAAAA,iBAAK;MAChBA,4BAAI;MAAAA,yBAAO;MAAAA,iBAAK;MAChBA,4BAAI;MAAAA,uBAAK;MAAAA,iBAAK;MACdA,4BAAI;MAAAA,wBAAM;MAAAA,iBAAK;MACfA,4BAAI;MAAAA,uBAAK;MAAAA,iBAAK;MACdA,4BAAI;MAAAA,sBAAI;MAAAA,iBAAK;MAGjBA,+BAAO;MACLA,gFAmBK;MACPA,iBAAQ;MAGZA,iCAA8B;MAG1BA;QAAA,OAASmG;UAAAa,KAAgBb,gBAAY,0BAA0B,CAAC;UAAA9B,KAAO8B,gBAAiB,wBAAwB,GAAG,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE;UAAAS,KAAO;QAAK,kBAAmB;MAAA,EAAC;MAEvK5G,iCAAqC;MAC7BA,2BAAS;MAAAA,iBAAO;MAMhCA,oCAAmD;MACjDA,iIAEc;MACdA,iCAA0B;MAKlBA,2BAAwB;MACxBA,4BAAI;MAAAA,yBAAE;MAAAA,iBAAK;MACXA,4BAAI;MAAAA,sBAAI;MAAAA,iBAAK;MACbA,4BAAI;MAAAA,6BAAW;MAAAA,iBAAK;MACpBA,4BAAI;MAAAA,6BAAW;MAAAA,iBAAK;MACpBA,4BAAI;MAAAA,uBAAK;MAAAA,iBAAK;MACdA,4BAAI;MAAAA,mCAAiB;MAAAA,iBAAK;MAC1BA,uBAAS;MACXA,iBAAK;MAEPA,+BAAO;MACLA,oGAmDe;MACjBA,iBAAQ;MAEVA,iCAA2D;MAI/CA,kEAAgD;MAAAA,iBAAO;MAE/DA,iIAYc;MAChBA,iBAAM;MAOlBA,8GAAgF;MAChFA,6CAAqD;;;;;;;;;;;;;MAhcOA,eAAwB;MAAxBA,wCAAwB;MAE/CA,eAAiC;MAAjCA,4DAAiC;MAKOA,eAAwB;MAAxBA,0CAAwB;MAoB/DA,eAAwB;MAAxBA,0CAAwB;MA2BrDA,eAAuD;MAAvDA,6EAAuD;MA2BhCA,eAAsB;MAAtBA,6BAAsB;MAURA,eAAqC;MAArCA,yDAAqC;MA8BnBA,eAE3C;MAF2CA,kEAE3C;MAcqBA,gBAA2B;MAA3BA,kCAA2B;MAkC5CA,eAA0B;MAA1BA,wCAA0B;MAI1BA,eAA0B;MAA1BA,wCAA0B;MAI1BA,eACyF;MADzFA,6HACyF;MAkDlFA,eAAkB;MAAlBA,8BAAkB;MAOUA,eAAyB;MAAzBA,2CAAyB;MAgB/CA,gBAA2B;MAA3BA,mCAA2B;MAkC/CA,eAAsJ;MAAtJA,uLAAsJ;MASxGA,eAAgC;MAAhCA,kDAAgC;MAgB5DA,gBAAiC;MAAjCA,mCAAiC;MAyBvDA,eAAoO;MAApOA,gRAAoO;MAUlNA,eAAwB;MAAxBA,8BAAwB;MAK3BA,eAAiD;MAAjDA,mEAAiD;MAejCA,gBAAe;MAAfA,mCAAe;MAwDhBA,eAAwB;MAAxBA,0CAAwB;MAwB/CA,eAAoC;MAApCA,yDAAoC", "names": ["TresorComposant", "i0", "ctx_r81", "CompteFournisseurComponent", "onExpandChange", "id", "checked", "expandSet", "add", "delete", "constructor", "app", "backendService", "datePipe", "Set", "code", "label", "ngOnInit", "setTitre", "getMtOperation", "data", "current_data", "montant", "find", "d", "selectFournisseur", "setDataString", "setDataList", "filtre", "frs", "undefined", "getEcriture", "getReceptionHistory", "split", "getEcrituresAvonces", "getDataList", "filter", "ecriture", "tpe_doc", "data_regl_paiement", "reste", "data_paiement", "data_tiers", "tpe_tiers", "getBr<PERSON><PERSON><PERSON>", "br", "status", "reel_rest", "vbl", "_chk", "reduce", "acc", "solde", "getFrs", "cl_data", "cl", "selctedAbr", "bl", "concat", "splice", "indexOf", "selctedAbrAvr", "ckavr", "imprimer", "date_debut", "transform", "date_fin", "f", "extractIdTiers", "ecritures", "ecr", "Date", "date_doc", "abr", "ab", "send2Printer", "calculSoldePrintFrs", "debit", "credit", "for<PERSON>ach", "total_debit", "total_credit", "is_avoir", "data_valeurs", "mt_ttc", "imprimerLet", "abrs", "imprimerDet", "pole", "getMontantAvoir", "obj", "o", "data_avoir", "mt", "selectors", "features", "decls", "vars", "consts", "template", "ctx", "cle", "_ttl", "_paiement", "sens", "nom", "tiers", "_qp", "avr", "tpe", "journal", "avonce", "withdet", "bls"], "sourceRoot": "", "sources": ["G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\compta\\modules\\tresor\\vues\\compte-fournisseur\\compte-fournisseur.component.ts", "G:\\newDev\\GStation\\topetrol\\front\\src\\app\\metier\\erp\\modules\\compta\\modules\\tresor\\vues\\compte-fournisseur\\compte-fournisseur.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AppService } from 'src/app/core/services/app.service';\nimport { BackEndService } from 'src/app/store/services/back-end.service';\nimport { TresorComposant } from '../../features/tresor.composant';\nimport { DatePipe } from '@angular/common';\n\n@Component({\n  selector: 'comp-compte-fournisseur',\n  templateUrl: './compte-fournisseur.component.html',\n  styleUrls: ['./compte-fournisseur.component.scss'],\n})\nexport class CompteFournisseurComponent extends TresorComposant implements OnInit {\n  expandSet = new Set<number>();\n  checked = true;\n  onExpandChange(id: number, checked: boolean): void {\n    if (checked) {\n      this.expandSet.add(id);\n    } else {\n      this.expandSet.delete(id);\n    }\n  }\n  liste_pole = [\n    {\n      code: 'lub',\n      label: 'Lubrifiant',\n    },\n    {\n      code: 'carb',\n      label: 'Carburant',\n    }\n  ]\n  constructor(\n    protected override app: AppService,\n    protected override backendService: BackEndService,\n    private datePipe: DatePipe\n  ) {\n    super(app, backendService);\n  }\n  ngOnInit(): void {\n    this.setTitre('Gestion des comptes fournisseurs');\n  }\n  getMtOperation(data: any, current_data: any) {\n    if (data) {\n      const montant = data.find((d: any) => d.id == current_data.id)\n\n      if (montant)\n        return montant.montant || 0;\n    }\n\n    return 0;\n  }\n  selectFournisseur() {\n    this.setDataString('abr.loading', '1')\n    this.setDataString('abrh.loading', '1')\n    this.setDataList('achat.liste_selected_avr', [])\n    if (this.filtre.frs != undefined) {\n      this.getEcriture(this.filtre.frs, ['ve', 'bq', 'ca', 'od'], false, true);\n      this.getReceptionHistory(this.filtre.frs.split('_')[1], false);\n\n    }\n  }\n\n  getEcrituresAvonces() {\n    return this.getDataList('compta.liste_ecritures').filter((ecriture: any) => (ecriture.tpe_doc === 'regl_frs') && ecriture.data_regl_paiement.reste > 0 && ecriture.data_paiement == null && ecriture.data_tiers.tpe_tiers == this.filtre?.frs?.split('_')[0]) || [];\n  }\n\n  getBrAvonces() {\n    return this.getDataList('achat.liste_abr').filter((br: any) => (br.data_paiement.status == 'Impayé' || br.data_paiement.status == 'En cours') && br.reel_rest >= 0);\n  }\n\n  reste() {\n    return this.getDataList('achat.liste_abr').filter(vbl => vbl._chk == true).reduce((acc, vbl) => acc + vbl.data_paiement.reste, 0);\n  }\n\n  montant() {\n    return this.getEcrituresAvonces().filter(vbl => vbl._chk == true).reduce((acc, vbl) => acc + vbl.solde, 0);\n  }\n\n  getFrs(cl_data: any) {\n    return this.getDataList(\n      `tiers.liste_${cl_data.split('_')[0]}`\n    ).find((cl) => cl.id == cl_data.split('_')[1]);\n  }\n\n  selctedAbr(bl: any) {\n    if (bl._chk) {\n      this.setDataList('achat.liste_selected_abr', this.getDataList('achat.liste_selected_abr').concat(bl));\n    }\n    else {\n      this.getDataList('achat.liste_selected_abr').splice(this.getDataList('achat.liste_selected_abr').indexOf(bl), 1);\n    }\n  }\n\n  selctedAbrAvr(bl: any) {\n    if (bl.ckavr) {\n      this.setDataList('achat.liste_selected_avr', this.getDataList('achat.liste_selected_avr').concat(bl));\n    }\n    else {\n      this.getDataList('achat.liste_selected_avr').splice(this.getDataList('achat.liste_selected_avr').indexOf(bl), 1);\n    }\n  }\n\n  imprimer() {\n    this.filtre.date_debut = this.datePipe.transform(this.filtre.date_debut, 'yyyy-MM-dd');\n    this.filtre.date_fin = this.datePipe.transform(this.filtre.date_fin, 'yyyy-MM-dd');\n    const frs = this.getDataList(\n      `tiers.liste_frs`\n    ).find((f) => f.id == this.extractIdTiers(this.filtre.frs));\n    var ecritures = this.getDataList('compta.liste_ecritures', [\n      'tiers',\n      '==',\n      this.extractIdTiers(this.filtre.frs)\n    ]);\n    ecritures = ecritures.filter(ecr => (new Date(ecr.date_doc) >= new Date(this.filtre.date_debut)) && (new Date(ecr.date_doc) <= new Date(this.filtre.date_fin)))\n    var abr = this.getDataList('achat.liste_abr', ['tiers', '==', this.extractIdTiers(this.filtre.frs)])\n    abr = this.getDataList('achat.liste_abr').filter(ab => (new Date(ab.date_doc) >= new Date(this.filtre.date_debut)) && (new Date(ab.date_doc) <= new Date(this.filtre.date_fin)))\n    if (this.checked) {\n      ecritures = ecritures.concat(abr);\n    }\n    else {\n      ecritures = abr;\n    }\n    this.send2Printer({\n      code: 'page_compte_fournisseur',\n      data: {\n        frs,\n        ecritures,\n        abr,\n        checked: this.checked,\n        date_debut: this.filtre.date_debut,\n        date_fin: this.filtre.date_fin,\n        solde: this.calculSoldePrintFrs().solde,\n        debit: this.calculSoldePrintFrs().debit,\n        credit: this.calculSoldePrintFrs().credit,\n      }\n    });\n  }\n\n  calculSoldePrintFrs() {\n    let solde = 0;\n    let debit = 0;\n    let credit = 0;\n    const frs = this.getDataList(\n      `tiers.liste_frs`\n    ).find((f) => f.id == this.extractIdTiers(this.filtre.frs));\n    var ecritures = this.getDataList('compta.liste_ecritures', [\n      'tiers',\n      '==',\n      this.extractIdTiers(this.filtre.frs)\n    ]);\n    ecritures = ecritures.filter(ecr => (new Date(ecr.date_doc) >= new Date(this.filtre.date_debut)) && (new Date(ecr.date_doc) <= new Date(this.filtre.date_fin)))\n    var abr = this.getDataList('achat.liste_abr', ['tiers', '==', this.extractIdTiers(this.filtre.frs)])\n    abr = this.getDataList('achat.liste_abr').filter(ab => (new Date(ab.date_doc) >= new Date(this.filtre.date_debut)) && (new Date(ab.date_doc) <= new Date(this.filtre.date_fin)))\n    if (ecritures) {\n      ecritures.forEach((ecriture: any) => {\n        debit += ecriture.total_debit;\n        credit += ecriture.total_credit;\n      });\n    }\n    abr.forEach((abr: any) => {\n      if (!abr.is_avoir) {\n        credit += abr.data_valeurs.mt_ttc || 0;\n      }\n      else {\n        debit += abr.data_valeurs.mt_ttc || 0;\n      }\n    });\n    return { debit, credit, solde: debit - credit };\n  }\n\n  imprimerLet(code: any) {\n    this.filtre.date_debut = this.datePipe.transform(this.filtre.date_debut, 'yyyy-MM-dd');\n    this.filtre.date_fin = this.datePipe.transform(this.filtre.date_fin, 'yyyy-MM-dd');\n    const frs = this.getDataList(\n      `tiers.liste_frs`\n    ).find((f) => f.id == this.extractIdTiers(this.filtre.frs));\n    if (frs) {\n      let abrs = this.getDataList('achat.liste_abr_history').filter(ecr => (new Date(ecr.date_doc) >= new Date(this.filtre.date_debut)) && (new Date(ecr.date_doc) <= new Date(this.filtre.date_fin)));\n      this.send2Printer({\n        code,\n        data: {\n          ecritures: abrs,\n          frs,\n          date_debut: this.filtre.date_debut,\n          date_fin: this.filtre.date_fin,\n        },\n      });\n    }\n  }\n  imprimerDet(code: any) {\n    this.filtre.date_debut = this.datePipe.transform(this.filtre.date_debut, 'yyyy-MM-dd');\n    this.filtre.date_fin = this.datePipe.transform(this.filtre.date_fin, 'yyyy-MM-dd');\n    const frs = this.getDataList(\n      `tiers.liste_frs`\n    ).find((f) => f.id == this.extractIdTiers(this.filtre.frs));\n    if (frs) {\n      let abr = this.getDataList('achat.liste_abr', ['tiers', '==', this.extractIdTiers(this.filtre.frs)]).filter(ecr => (new Date(ecr.date_doc) >= new Date(this.filtre.date_debut)) && (new Date(ecr.date_doc) <= new Date(this.filtre.date_fin)));\n      this.send2Printer({\n        code,\n        data: {\n          ecritures: abr,\n          frs,\n          date_debut: this.filtre.date_debut,\n          date_fin: this.filtre.date_fin,\n          pole: this.filtre.pole || 'lub'\n        },\n      });\n    }\n  }\n\n  getMontantAvoir(obj: any) {\n    let montant = 0;\n    for (let o of obj.data_avoir) {\n      montant += o.mt || 0;\n    }\n    return montant;\n  }\n}\n", "<div class=\"panel min-h-[80vh]\">\n  <div class=\"flex gap-2 justify-between w-full\">\n    <div class=\"flex gap-2 w-full\">\n      <form-item label=\"Fournisseur\" class=\"w-[20%] align-middle\">\n        <nz-select nzAllowClear class=\"w-full\" nzShowSearch [(ngModel)]=\"filtre.frs\"\n          (ngModelChange)=\"selectFournisseur()\">\n          <nz-option *ngFor=\"let frs of getDataList('tiers.liste_frs')\" [nzLabel]=\"frs.nom_tiers\"\n            [nzValue]=\"'frs_'+frs.id\"></nz-option>\n        </nz-select>\n      </form-item>\n      <div class=\"w-auto self-end\">\n        <a nz-button nzType=\"primary\" nz-popover nzPopoverPlacement=\"bottom\" [disabled]=\"!filtre.frs\"\n          [nzPopoverContent]=\"formPrint\" nzPopoverTrigger=\"click\" class=\"\">\n          <span>Imprimer</span>\n        </a>\n        <ng-template #formPrint>\n          <div class=\"w-[20vw] flex gap-2\">\n            <form-item label=\"Date Début\">\n              <nz-date-picker class=\"w-full\" [(ngModel)]=\"filtre.date_debut\"></nz-date-picker>\n            </form-item>\n            <form-item label=\"Date Fin\">\n              <nz-date-picker class=\"w-full\" [(ngModel)]=\"filtre.date_fin\"></nz-date-picker>\n            </form-item>\n          </div>\n          <label class=\"pt-3\" nz-checkbox [(ngModel)]=\"checked\">Avec réglement</label>\n          <div class=\"text-right pt-3\">\n            <button nz-button (click)=\"imprimer()\">Imprimer</button>\n          </div>\n        </ng-template>\n      </div>\n      <div class=\"pt-2 self-end\">\n        <a nz-button nzType=\"primary\" [disabled]=\"!filtre.frs\" nz-popover nzPopoverPlacement=\"top\" style=\"width: 100%;\"\n          [nzPopoverContent]=\"formPrintlet\" nzPopoverTrigger=\"click\" class=\"\">\n          <span>Impression detaillé</span>\n        </a>\n        <ng-template #formPrintlet>\n          <div class=\"w-[20vw] flex gap-2\">\n            <form-item label=\"Date Début\">\n              <nz-date-picker class=\"w-full\" [(ngModel)]=\"filtre.date_debut\"></nz-date-picker>\n            </form-item>\n            <form-item label=\"Date Fin\">\n              <nz-date-picker class=\"w-full\" [(ngModel)]=\"filtre.date_fin\"></nz-date-picker>\n            </form-item>\n          </div>\n          <div class=\"w-[15vw]\">\n            <form-item label=\"Pole\">\n              <nz-select class=\"w-full\" nzAllowClear nzShowSearch [(ngModel)]=\"filtre.pole\">\n                <nz-option *ngFor=\"let tpe of liste_pole\" [nzLabel]=\"tpe.label\" [nzValue]=\"tpe.code\"></nz-option>\n              </nz-select>\n            </form-item>\n          </div>\n          <div class=\"text-right pt-3\">\n            <button nz-button (click)=\"imprimerDet('page_compte_frs_det')\">Imprimer</button>\n          </div>\n        </ng-template>\n      </div>\n    </div>\n    <div>\n      <a [disabled]=\"!achatCommercialPrivilege() || !filtre.frs\" nz-button nzType=\"primary\"\n        class=\"uppercase float-right\" (click)=\"\n          openForm({\n            cle: 'form_paiement',\n            _ttl: 'Nouveau Paiement',\n            _paiement: {\n              tpe_doc: 'regl_frs',\n              sens: 's',\n              data_tiers:{\n                nom: getFrs(filtre.frs).nom_tiers,\n                tpe_tiers: filtre.frs.split('_')[0]\n              },\n              tiers: getFrs(filtre.frs).id\n            },\n            _qp: { tiers: filtre.frs }\n          })\n        \">\n        <div class=\"flex items-center gap-2\">\n          <span nz-icon nzType=\"plus\" nzTheme=\"outline\"></span>\n          <span>Créer Paiement</span>\n        </div>\n      </a>\n    </div>\n  </div>\n\n  <div>\n    <nz-tabset>\n      <nz-tab class=\"uppercase\" [nzTitle]=\"ttlProduit\">\n        <ng-template #ttlProduit>\n          <div class=\"font-bold uppercase\">Compte Fournisseur</div>\n        </ng-template>\n        <div class=\"basis-[80%]\">\n          <div class=\"basis-[100%]\">\n            <ng-container>\n              <div class=\"flex gap-2\">\n                <div class=\"w-[50%]\">\n                  <div class=\"h-[60vh]\">\n                    <nz-table nzSize=\"small\" *ngIf=\"getDataList('achat.liste_abr') as listeDocs\" #tableDocs\n                      [nzLoading]=\"getDataString('abr.loading') == '1'\" [nzData]=\"listeDocs\">\n                      <thead>\n                        <tr>\n                          <th>N°</th>\n                          <th>Type</th>\n                          <th>Date</th>\n                          <th>Montant TTC</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        <tr *ngFor=\"let obj of tableDocs.data\" [ngClass]=\"{'bg-red-50': obj?.is_avoir}\">\n                          <td>\n                            <div>{{ obj.numero_interne }}</div>\n                            <span class=\"text-sm text-yellow-600\">{{obj.numero_externe}}</span>\n                          </td>\n                          <td>\n                            {{ obj.tpe_doc }}\n                          </td>\n                          <td>\n                            <span>{{ obj.date_doc | date : \"dd-MM-yyyy\" }}</span>\n                          </td>\n                          <td>{{ obj.is_avoir? -obj.data_valeurs.mt_ttc: obj.data_valeurs.mt_ttc || 0 | number : \".2-2\"\n                            : \"fr\" }}</td>\n                        </tr>\n                      </tbody>\n                    </nz-table>\n                  </div>\n                </div>\n                <div class=\"w-[50%]\">\n                  <nz-table nzSize=\"small\" #tablelisteEcritures [nzData]=\"\n                      getDataList('compta.liste_ecritures')\n                    \" [nzLoading]=\"getDataString('abr.loading') == '1'\">\n                    <thead>\n                      <tr>\n                        <!-- <th nzWidth=\"2vw\"></th> -->\n                        <th>Pièces</th>\n                        <!-- <th>Type</th> -->\n                        <th>Journal</th>\n                        <th>Montant</th>\n                        <th>Debit</th>\n                        <th>Credit</th>\n                        <th>Etat</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      <tr *ngFor=\"let obj of tablelisteEcritures.data\"\n                        [class]=\"obj?.data_etat?.etat=='inpaye'?'text-red-700':obj?.data_etat?.etat=='encaisse'?'text-green-600':''\">\n                        <!-- <td><label nz-checkbox [(ngModel)]=\"obj._chk\"></label></td> -->\n                        <td>\n                          <div class=\"uppercase\">\n                            {{ obj.intitule || obj.mode_paiement }}\n                          </div>\n                          <div class=\"font-medium text-sm text-gray-400\">\n                            {{ obj.date_echeance? obj.date_echeance: obj.date_doc | date : \"dd-MM-yyyy\" }}\n                          </div>\n                        </td>\n                        <!-- <td class=\"uppercase\">{{ obj.tpe_doc }}</td> -->\n                        <td class=\"uppercase\">{{ obj.journal }}</td>\n                        <td>{{ obj.data_valeurs?.mt_ttc || obj.solde | number : \".2-2\" }}</td>\n                        <td>\n                          <span *ngIf=\"obj.total_debit > 0\">{{\n                            obj.total_debit | number : \".2-2\"\n                            }}</span>\n                        </td>\n                        <td>\n                          <span *ngIf=\"obj.total_credit > 0\">{{\n                            obj.total_credit | number : \".2-2\"\n                            }}</span>\n                        </td>\n                        <td class=\"uppercase\">\n                          {{ obj.data_etat?.etat=='draft'?'En attente':obj.data_etat?.etat=='sent'?'Envoyé à la  banque':obj.data_etat?.etat=='inpaye'?'Impayé':'Encaissé' }}\n                        </td>\n                      </tr>\n                    </tbody>\n                  </nz-table>\n                </div>\n              </div>\n              <div class=\"border-0 p-1 flex gap-2 justify-end items-end\">\n                <form-item label=\"Debit\">\n                  <input [ngClass]=\"'bg-[#FF7B7B]'\" nz-input readonly class=\"font-bold text-lg\"\n                    [ngModel]=\"calculSoldeFrs('solde',filtre.frs).debit | number : '.02-2'\" />\n                </form-item>\n                <form-item label=\"Credit\">\n                  <input [ngClass]=\"'bg-[#A1D99B]'\" nz-input readonly class=\"font-bold text-lg\"\n                    [ngModel]=\"calculSoldeFrs('solde',filtre.frs).credit | number : '.02-2'\" />\n                </form-item>\n                <form-item label=\"Solde\">\n                  <input [ngClass]=\"\n                      calculSoldeFrs('solde',filtre.frs).solde >= 0 ? 'bg-[#A1D99B]' : 'bg-[#FF7B7B] text-black' \"\n                    nz-input readonly class=\"font-bold text-lg\"\n                    [ngModel]=\"calculSoldeFrs('solde',filtre.frs).solde | number : '.02-2'\" />\n                </form-item>\n              </div>\n            </ng-container>\n            <ng-template #AutresClients>\n              <nz-table nzSize=\"small\" #tablelisteVentes [nzData]=\"\n                  getDataList('compta.liste_ecritures', [ 'tiers', '==',  extractIdTiers(filtre.client)   ]) \"\n                [nzLoading]=\"getDataString('abr.loading') == '1'\">\n                <thead>\n                  <tr>\n                    <th nzWidth=\"2vw\"></th>\n                    <th>Pièces</th>\n                    <th>Type</th>\n                    <th>Journal</th>\n                    <!-- <th>Montant</th> -->\n                    <th>Debit</th>\n                    <th>Credit</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr *ngFor=\"let obj of tablelisteVentes.data\">\n                    <td><label nz-checkbox [(ngModel)]=\"obj._chk\"></label></td>\n                    <td>\n                      <div class=\"uppercase\">\n                        {{ obj.intitule || obj.mode_paiement }}\n                      </div>\n                      {{ obj.date_doc | date : \"dd-MM-yyyy\" }}\n                    </td>\n                    <td class=\"uppercase\">{{ obj.tpe_doc }}</td>\n                    <td class=\"uppercase\">{{ obj.journal }}</td>\n                    <!-- <td>{{ obj.data_valeurs?.mt_ttc || obj.solde | number : \".2-2\" }}</td> -->\n                    <td>\n                      <span *ngIf=\"obj.total_debit > 0\">{{\n                        obj.total_debit | number : \".2-2\"\n                        }}</span>\n                    </td>\n                    <td>\n                      <span *ngIf=\"obj.total_credit > 0\">{{\n                        obj.total_credit | number : \".2-2\"\n                        }}</span>\n                    </td>\n                  </tr>\n                </tbody>\n              </nz-table>\n            </ng-template>\n          </div>\n        </div>\n      </nz-tab>\n      <nz-tab class=\"uppercase\" [nzTitle]=\"ttlLub\">\n        <ng-template #ttlLub>\n          <div class=\"font-bold uppercase\">Avances</div>\n        </ng-template>\n        <div class=\"basis-[100%]\">\n          <div class=\"grid grid-cols-2 gap-2\">\n            <div>\n              <nz-table nzSize=\"small\" #tablelisteabrImpaye [nzData]=\"getBrAvonces()\"\n                [nzLoading]=\"getDataString('abr.loading') == '1'\">\n                <thead>\n                  <tr>\n                    <th nzWidth=\"2vw\"></th>\n                    <th>N°</th>\n                    <th>Type</th>\n                    <th>Montant TTC</th>\n                    <th>M. Paiement</th>\n                    <th>Avoir</th>\n                    <th>Reste</th>\n                    <th>Reste reel</th>\n                    <th></th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr *ngFor=\"let obj of tablelisteabrImpaye.data\"  [ngClass]=\"{'bg-red-50': obj?.is_avoir}\">\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\">\n                      <span nz-checkbox (nzCheckedChange)=\"selctedAbr(obj)\" [disabled]=\"obj?.reel_rest<=0\"\n                        [(ngModel)]=\"obj._chk\"></span>\n                    </td>\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\"\n                      class=\"uppercase\">{{ obj.numero_interne }}</td>\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\"\n                      class=\"uppercase\">{{ obj.tpe_doc }}</td>\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\">{{\n                      obj.data_valeurs?.mt_ttc || 0 | number :\n                      \".2-2\" }}</td>\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\">{{\n                      obj.data_paiement.montant_paiement | number :\n                      \".2-2\" }}</td>\n                    <td\n                      [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100 text-red-700':obj?.is_avoir? 'bg-red-100 text-red-700':'text-red-700'\"\n                      >-{{ obj.data_avoir.length>0? getMontantAvoir(obj):0 | number : \".2-2\" }}</td>\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\">{{\n                      obj.data_paiement.reste | number : \".2-2\" }}\n                    </td>\n\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\">{{\n                      obj.reel_rest - (obj.data_avoir.length>0? getMontantAvoir(obj):0)| number : \".2-2\" }}</td>\n                    <td [class]=\"obj?.reel_rest<=0 && obj?.is_avoir ? 'bg-green-100':obj?.is_avoir? 'bg-red-100':''\">\n                      <span nz-checkbox (nzCheckedChange)=\"selctedAbrAvr(obj)\"\n                        [disabled]=\"getDataList('achat.liste_selected_avr').length>=2 && !obj.ckavr\"\n                        [(ngModel)]=\"obj.ckavr\"></span>\n                    </td>\n                  </tr>\n                </tbody>\n              </nz-table>\n              <div class=\"basis-[80%] py-2\">\n                <a nz-button nzType=\"primary\" class=\"uppercase float-right\"\n                  [disabled]=\"!achatCommercialPrivilege() || !getDataList('achat.liste_abr',['ckavr', '==', true]).length || getDataString('confirmabr.loading') == '1'\"\n                  (click)=\"majBr({avr: getDataList('achat.liste_selected_avr',['tpe_doc', '==', 'avrf']), br: getDataList('achat.liste_selected_avr',['tpe_doc', '==', 'abr']), tpe: 'frs'}, {tiers: this.filtre.frs, journal: ['ve', 'bq', 'ca', 'od'], avonce:false, withdet :true}) \"\n                  [nzLoading]=\"getDataString('confirmabr.loading') == '1'\">\n                  <div class=\"flex items-center gap-2\">\n                    <span>Confirmer</span>\n                  </div>\n                </a>\n              </div>\n            </div>\n            <nz-table nzSize=\"small\" #tablelisteEcrituresAvonce [nzData]=\"getEcrituresAvonces()\"\n              [nzLoading]=\"getDataString('abr.loading') == '1'\">\n              <thead>\n                <tr>\n                  <th nzWidth=\"2vw\"></th>\n                  <th>Pièces</th>\n                  <th>Type</th>\n                  <th>Journal</th>\n                  <th>Montant</th>\n                  <th>Debit</th>\n                  <th>Credit</th>\n                  <th>Reste</th>\n                  <th>Etat</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let obj of tablelisteEcrituresAvonce.data\"\n                  [class]=\"obj?.data_etat?.etat=='inpaye'?'text-red-700':obj?.data_etat?.etat=='encaisse'?'text-green-600':''\">\n                  <td><label nz-checkbox [(ngModel)]=\"obj._chk\"></label></td>\n                  <td>\n                    <div class=\"uppercase\">{{ obj.intitule || obj.mode_paiement }}</div>\n                    {{  obj.date_echeance? obj.date_echeance: obj.date_doc | date : \"dd-MM-yyyy\" | date : \"dd-MM-yyyy\"  }}\n                  </td>\n                  <td class=\"uppercase\">{{ obj.tpe_doc }}</td>\n                  <td class=\"uppercase\">{{ obj.journal }}</td>\n                  <td>{{ obj.data_valeurs?.mt_ttc || obj.solde | number : \".2-2\" }}</td>\n                  <td><span *ngIf=\"obj.total_debit>0\">{{ obj.total_debit | number : \".2-2\" }}</span></td>\n                  <td><span *ngIf=\"obj.total_credit>0\">{{ obj.total_credit | number : \".2-2\" }}</span></td>\n                  <td><span>{{ obj.data_regl_paiement.reste | number : \".2-2\" }}</span></td>\n                  <td class=\"uppercase\">\n                    <span *ngIf=\"obj?.data_etat?.etat\">\n                      {{ obj.data_etat.etat=='draft'?'En attente':obj.data_etat.etat=='sent'?'Envoyé à la  banque':obj.data_etat.etat=='inpaye'?'Impayé':obj.data_etat.etat=='compta'?'Comptabilisée':'Encaissé'\n                      }}\n                    </span>\n                  </td>\n                </tr>\n              </tbody>\n            </nz-table>\n          </div>\n          <div class=\"basis-[80%] py-2\">\n            <a nz-button nzType=\"primary\" class=\"uppercase float-right\"\n              [disabled]=\"!achatCommercialPrivilege() || !getDataList('achat.liste_abr', ['_chk', '==', true]).length || !(getDataList('compta.liste_ecritures', ['_chk', '==', true]).length == 1) || getDataString('confirmavc.loading') == '1'\"\n              (click)=\"majAvonce({bls: getDataList('achat.liste_selected_abr'), ecr: this.getDataList('compta.liste_ecritures', ['_chk', '==', true]), tpe: 'frs'}, this.filtre.frs)\"\n              [nzLoading]=\"getDataString('confirmavc.loading') == '1'\">\n              <div class=\"flex items-center gap-2\">\n                <span>Confirmer</span>\n              </div>\n            </a>\n          </div>\n        </div>\n      </nz-tab>\n      <nz-tab class=\"uppercase\" [nzTitle]=\"titleLitrage\">\n        <ng-template #titleLitrage>\n          <div class=\"font-bold uppercase\">Historique de lettrege des reglements</div>\n        </ng-template>\n        <div class=\"basis-[100%]\">\n          <nz-table #nzTable [nzData]=\"getDataList('achat.liste_abr_history')\" nzTableLayout=\"fixed\"\n            [nzLoading]=\"getDataString('abrh.loading') == '1'\">\n            <thead>\n              <tr>\n                <th nzWidth=\"50px\"></th>\n                <th>N°</th>\n                <th>Date</th>\n                <th>Montant TTC</th>\n                <th>Montant TTC</th>\n                <th>Reste</th>\n                <th>Situation paiment</th>\n                <th></th>\n              </tr>\n            </thead>\n            <tbody>\n              <ng-container *ngFor=\"let data of nzTable.data\">\n                <tr>\n                  <td [nzExpand]=\"expandSet.has(data.id)\" (nzExpandChange)=\"onExpandChange(data.id, $event)\"></td>\n                  <td>{{ data.numero_interne }}</td>\n                  <td>{{ data.date_doc | date : \"dd-MM-yyyy\" }}</td>\n                  <td>{{ data.data_valeurs.mt_ttc || 0 | number : \".2-2\" }}</td>\n                  <td>{{ data.data_paiement.montant_paiement | number : \".2-2\" }}</td>\n                  <td>{{ data.data_paiement.reste | number : \".2-2\" }}</td>\n                  <td class=\"uppercase\">\n                    <nz-tag\n                      [nzColor]=\"data.data_paiement.status == 'En cours'?'orange':data.data_paiement.status == 'Impayé'?'red':'green'\">{{data.data_paiement.status}}</nz-tag>\n                  </td>\n                  <td class=\"uppercase\">\n                    <a nz-button nzType=\"link\" nz-popconfirm [nzPopconfirmTitle]=\"Messages.confirm_update_bl\"\n                      nzPopconfirmPlacement=\"bottom\" [disabled]=\"!adminPrivilege()\"  \n                      [nzLoading]=\"getDataString('reloadbr.loading') == '1'\"\n                      (nzOnConfirm)=\"adminPrivilege() && ReloadBr(data, filtre.frs)\">\n                      <span nz-icon nzType=\"reload\" nzTheme=\"outline\"></span>\n                    </a>                  \n                  </td>\n                </tr>\n                <tr [nzExpand]=\"expandSet.has(data.id)\">\n                  <div class=\"block w-full space-y-4 overflow-x-auto rounded-lg border border-white-dark/20 p-4\">\n                    <ng-container *ngIf=\"data.data_avoir.length>0\">\n                      <ng-container *ngFor=\"let obj of data.data_avoir\">\n                        <div\n                          class=\"bg-red-50 flex min-w-[625px] items-center justify-around rounded-xl p-3 font-semibold text-gray-500 shadow-[0_0_4px_2px_rgb(31_45_61_/_10%)] transition-all duration-300 hover:scale-[1.01] \">\n                          <div> Avoir </div>\n                          <div class=\"text-red-700\"> -{{obj.mt | number : \".2-2\" : \"fr\"}} DH </div>\n                        </div>\n                      </ng-container>\n                    </ng-container>\n                    <ng-container *ngFor=\"let obj of data.ecritures\">\n                      <div [ngClass]=\"obj.data_etat.etat == 'draft'?'bg-red-50':'bg-white '\"\n                        class=\"flex min-w-[625px] items-center justify-between rounded-xl p-3 font-semibold text-gray-500 shadow-[0_0_4px_2px_rgb(31_45_61_/_10%)] transition-all duration-300 hover:scale-[1.01] \">\n                        <div>\n                          {{ obj.date_doc | date : \"dd-MM-yyyy\" }}\n                        </div>\n                        <div> {{ obj.numero_externe || obj.intitule}} </div>\n                        <div class=\"text-green-700\"> {{ (obj.data_multiple_paiement.length>0 &&\n                          getMtOperation(obj.data_multi_det, data )>0?getMtOperation(obj.data_multi_det, data ):\n                          getMtOperation(obj.data_multi_det, data)==0?obj.solde: obj.solde) | number : \".2-2\" : \"fr\"}}\n                          DH\n                        </div>\n                        <div> {{ obj.solde | number : \".2-2\" : \"fr\"}} DH </div>\n                        <div> {{ obj.mode_paiement | lbl : \"commun.liste_mode_paiement\" : \"code\" : \"label\" }} </div>\n                      </div>\n                    </ng-container>\n                  </div>\n                </tr>\n                <tr></tr>\n              </ng-container>\n            </tbody>\n          </nz-table>\n          <div class=\"border-0 p-1 flex gap-2 justify-end items-end\">\n            <div class=\"pt-2 w-auto\">\n              <a nz-button nzType=\"primary\" [disabled]=\"!filtre.frs\" nz-popover nzPopoverPlacement=\"top\"\n                style=\"width: 100%;\" [nzPopoverContent]=\"formPrintdet\" nzPopoverTrigger=\"click\" class=\"\">\n                <span>Impression Historique de lettrage des reglements</span>\n              </a>\n              <ng-template #formPrintdet>\n                <div class=\"w-[20vw] flex gap-2\">\n                  <form-item label=\"Date Début\">\n                    <nz-date-picker class=\"w-full\" [(ngModel)]=\"filtre.date_debut\"></nz-date-picker>\n                  </form-item>\n                  <form-item label=\"Date Fin\">\n                    <nz-date-picker class=\"w-full\" [(ngModel)]=\"filtre.date_fin\"></nz-date-picker>\n                  </form-item>\n                </div>\n                <div class=\"text-right pt-3\">\n                  <button nz-button (click)=\"imprimerLet('page_compte_frs_let')\">Imprimer</button>\n                </div>\n              </ng-template>\n            </div>\n          </div>\n        </div>\n      </nz-tab>\n    </nz-tabset>\n  </div>\n</div>\n<comp-form-paiement *ngIf=\"isFormVisible('form_paiement')\"></comp-form-paiement>\n<compte-fournisseur-print></compte-fournisseur-print>\n<compte-frs-print-let></compte-frs-print-let>\n<compte-fournisseur-print-det></compte-fournisseur-print-det>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}